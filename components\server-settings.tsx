"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Save, RotateCcw } from 'lucide-react'

interface ServerSettingsProps {
  server: any
}

export default function ServerSettings({ server }: ServerSettingsProps) {
  return (
    <div className="space-y-6">
      {/* General Settings */}
      <Card className="glass-ultra border border-border shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground">General Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="server-name" className="text-foreground">Server Name</Label>
              <Input
                id="server-name"
                defaultValue={server.name}
                className="glass-ultra border-border text-foreground"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max-players" className="text-foreground">Max Players</Label>
              <Input
                id="max-players"
                type="number"
                defaultValue="20"
                className="glass-ultra border-border text-foreground"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="server-description" className="text-foreground">Server Description</Label>
            <Textarea
              id="server-description"
              placeholder="Enter server description..."
              className="glass-ultra border-border text-foreground placeholder:text-muted-foreground"
              rows={3}
            />
          </div>

          <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
            <div>
              <Label htmlFor="whitelist" className="text-foreground font-medium">Enable Whitelist</Label>
              <p className="text-muted-foreground text-sm">Only allow whitelisted players to join</p>
            </div>
            <Switch id="whitelist" />
          </div>

          <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
            <div>
              <Label htmlFor="pvp" className="text-foreground font-medium">Enable PvP</Label>
              <p className="text-muted-foreground text-sm">Allow player vs player combat</p>
            </div>
            <Switch id="pvp" defaultChecked />
          </div>
        </CardContent>
      </Card>

      {/* Resource Limits */}
      <Card className="glass-ultra border border-border shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground">Resource Limits</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="memory-limit" className="text-foreground">Memory Limit (MB)</Label>
              <Input
                id="memory-limit"
                type="number"
                defaultValue="4096"
                className="glass-ultra border-border text-foreground"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cpu-limit" className="text-foreground">CPU Limit (%)</Label>
              <Input
                id="cpu-limit"
                type="number"
                defaultValue="100"
                className="glass-ultra border-border text-foreground"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="disk-limit" className="text-foreground">Disk Space (GB)</Label>
              <Input
                id="disk-limit"
                type="number"
                defaultValue="10"
                className="glass-ultra border-border text-foreground"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="network-limit" className="text-foreground">Network (Mbps)</Label>
              <Input
                id="network-limit"
                type="number"
                defaultValue="100"
                className="glass-ultra border-border text-foreground"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button variant="outline" className="secondary-button">
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset to Defaults
        </Button>
        <Button className="premium-button">
          <Save className="w-4 h-4 mr-2" />
          Save Changes
        </Button>
      </div>
    </div>
  )
}
