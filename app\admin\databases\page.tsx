"use client"

import { useState } from "react"
import AdminLayout from "@/components/admin-layout"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { CreateDatabaseDialog } from "@/components/create-database-dialog"
import { DeleteConfirmationDialog } from "@/components/delete-confirmation-dialog"
import { Database, Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Server, Users, Activity, Calendar } from 'lucide-react'

interface DatabaseItem {
  id: string
  name: string
  type: "MySQL" | "PostgreSQL" | "MongoDB" | "Redis"
  server: string
  size: string
  status: "active" | "inactive" | "maintenance"
  connections: number
  maxConnections: number
  lastAccessed: string
  createdAt: string
}

const mockDatabases: DatabaseItem[] = [
  {
    id: "1",
    name: "minecraft_survival",
    type: "MySQL",
    server: "Survival Server",
    size: "2.4 GB",
    status: "active",
    connections: 15,
    maxConnections: 100,
    lastAccessed: "2 minutes ago",
    createdAt: "2024-01-15"
  },
  {
    id: "2",
    name: "creative_builds",
    type: "PostgreSQL",
    server: "Creative Server",
    size: "1.8 GB",
    status: "active",
    connections: 8,
    maxConnections: 50,
    lastAccessed: "1 hour ago",
    createdAt: "2024-01-10"
  },
  {
    id: "3",
    name: "player_cache",
    type: "Redis",
    server: "Proxy Server",
    size: "512 MB",
    status: "maintenance",
    connections: 0,
    maxConnections: 200,
    lastAccessed: "3 hours ago",
    createdAt: "2024-01-08"
  },
  {
    id: "4",
    name: "analytics_data",
    type: "MongoDB",
    server: "Analytics Server",
    size: "5.2 GB",
    status: "inactive",
    connections: 0,
    maxConnections: 25,
    lastAccessed: "1 day ago",
    createdAt: "2024-01-05"
  }
]

export default function AdminDatabasesPage() {
  const [databases, setDatabases] = useState<DatabaseItem[]>(mockDatabases)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedDatabase, setSelectedDatabase] = useState<DatabaseItem | null>(null)

  const filteredDatabases = databases.filter(db => {
    const matchesSearch = db.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         db.server.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || db.status === statusFilter
    const matchesType = typeFilter === "all" || db.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const handleCreateDatabase = (data: any) => {
    const newDatabase: DatabaseItem = {
      id: Date.now().toString(),
      name: data.name,
      type: data.type,
      server: data.server,
      size: "0 MB",
      status: "active",
      connections: 0,
      maxConnections: data.maxConnections || 100,
      lastAccessed: "Never",
      createdAt: new Date().toISOString().split('T')[0]
    }
    
    setDatabases(prev => [newDatabase, ...prev])
    setCreateDialogOpen(false)
  }

  const handleDeleteDatabase = () => {
    if (selectedDatabase) {
      setDatabases(prev => prev.filter(db => db.id !== selectedDatabase.id))
      setDeleteDialogOpen(false)
      setSelectedDatabase(null)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      case "inactive":
        return "bg-gray-500/10 text-gray-500 border-gray-500/20"
      case "maintenance":
        return "bg-yellow-500/10 text-yellow-500 border-yellow-500/20"
      default:
        return "bg-gray-500/10 text-gray-500 border-gray-500/20"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "MySQL":
        return "bg-blue-500/10 text-blue-500 border-blue-500/20"
      case "PostgreSQL":
        return "bg-purple-500/10 text-purple-500 border-purple-500/20"
      case "MongoDB":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      case "Redis":
        return "bg-red-500/10 text-red-500 border-red-500/20"
      default:
        return "bg-gray-500/10 text-gray-500 border-gray-500/20"
    }
  }

  // Stats calculation
  const totalDatabases = databases.length
  const activeDatabases = databases.filter(db => db.status === "active").length
  const totalConnections = databases.reduce((sum, db) => sum + db.connections, 0)
  const totalSize = databases.reduce((sum, db) => {
    const sizeValue = parseFloat(db.size.split(' ')[0])
    const unit = db.size.split(' ')[1]
    if (unit === "GB") return sum + (sizeValue * 1024)
    return sum + sizeValue
  }, 0)

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Database Management</h1>
            <p className="text-muted-foreground">Manage and monitor your databases</p>
          </div>
          <Button 
            onClick={() => setCreateDialogOpen(true)}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Database
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="glass-ultra border border-border rounded-xl shadow-glass">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Databases</p>
                  <p className="text-2xl font-bold text-foreground">{totalDatabases}</p>
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                  <Database className="w-5 h-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-xl shadow-glass">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-green-500">{activeDatabases}</p>
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                  <Activity className="w-5 h-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-xl shadow-glass">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Connections</p>
                  <p className="text-2xl font-bold text-purple-500">{totalConnections}</p>
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-xl shadow-glass">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Size</p>
                  <p className="text-2xl font-bold text-orange-500">{(totalSize / 1024).toFixed(1)} GB</p>
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <Server className="w-5 h-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="glass-ultra border border-border rounded-xl shadow-glass">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search databases..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-background/50 border-border/50"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[140px] bg-background/50 border-border/50">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full sm:w-[140px] bg-background/50 border-border/50">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="MySQL">MySQL</SelectItem>
                  <SelectItem value="PostgreSQL">PostgreSQL</SelectItem>
                  <SelectItem value="MongoDB">MongoDB</SelectItem>
                  <SelectItem value="Redis">Redis</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Database Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredDatabases.map((database) => (
            <Card key={database.id} className="glass-ultra border border-border rounded-xl shadow-glass hover:shadow-glass-lg transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg font-semibold text-foreground break-words line-clamp-2">
                      {database.name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1 truncate">{database.server}</p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="flex-shrink-0 ml-2 z-10">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => {
                          setSelectedDatabase(database)
                          setDeleteDialogOpen(true)
                        }}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className={getTypeColor(database.type)}>
                    {database.type}
                  </Badge>
                  <Badge className={getStatusColor(database.status)}>
                    {database.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Size</p>
                    <p className="font-medium text-foreground">{database.size}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Connections</p>
                    <p className="font-medium text-foreground">
                      {database.connections}/{database.maxConnections}
                    </p>
                  </div>
                </div>

                <div className="text-sm">
                  <p className="text-muted-foreground">Last accessed</p>
                  <p className="font-medium text-foreground">{database.lastAccessed}</p>
                </div>

                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Calendar className="w-3 h-3" />
                  Created {database.createdAt}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredDatabases.length === 0 && (
          <Card className="glass-ultra border border-border rounded-xl shadow-glass">
            <CardContent className="p-8 text-center">
              <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">No databases found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== "all" || typeFilter !== "all" 
                  ? "Try adjusting your search or filters"
                  : "Create your first database to get started"
                }
              </p>
              {!searchTerm && statusFilter === "all" && typeFilter === "all" && (
                <Button 
                  onClick={() => setCreateDialogOpen(true)}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Database
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      <CreateDatabaseDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateDatabase}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteDatabase}
        title="Delete Database"
        description={`Are you sure you want to delete "${selectedDatabase?.name}"? This action cannot be undone and all data will be permanently lost.`}
      />
    </AdminLayout>
  )
}
