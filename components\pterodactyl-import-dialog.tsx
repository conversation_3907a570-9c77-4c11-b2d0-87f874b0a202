"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTit<PERSON> } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useState } from "react"
import { useServerStore } from "@/stores/server-store"
import { useToast } from "@/hooks/use-toast"

export default function PterodactylImportDialog({ open, onOpenChange }: { open: boolean; onOpenChange: (v: boolean) => void }) {
  const [url, setUrl] = useState("")
  const [key, setKey] = useState("")
  const { toast } = useToast()
  const { create } = useServerStore()

  const importNow = () => {
    // Mock import; in real-life, call Pterodactyl API and map to store:
    create({ name: "Imported • Skyblock", type: "Minecraft", status: "online", players: "3/20", cpu: "12%", memory: "1.2GB/4GB", uptime: "4h 10m" })
    create({ name: "Imported • Rust EU", type: "Rust", status: "offline", players: "0/100", cpu: "0%", memory: "0/16GB", uptime: "Offline" })
    toast({ title: "Imported", description: "Servers imported from Pterodactyl." })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="glass-ultra border border-border shadow-glass">
        <DialogHeader><DialogTitle className="text-foreground">Import from Pterodactyl</DialogTitle></DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="text-foreground">Panel URL</Label>
            <Input value={url} onChange={(e) => setUrl(e.target.value)} placeholder="https://panel.example.com" className="glass-ultra border-border" />
          </div>
          <div className="space-y-2">
            <Label className="text-foreground">API Key</Label>
            <Input value={key} onChange={(e) => setKey(e.target.value)} placeholder="ptla_xxx" className="glass-ultra border-border" />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" className="secondary-button" onClick={() => onOpenChange(false)}>Cancel</Button>
            <Button className="premium-button" onClick={importNow}>Import</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
