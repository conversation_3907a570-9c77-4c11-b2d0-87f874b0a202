"use client"

export default function LoadingSkeleton({ className = "" }: { className?: string }) {
  return <div className={`loading-shimmer rounded-lg ${className}`} />
}

export function ServerCardSkeleton() {
  return (
    <div className="glass-ultra border border-border rounded-2xl p-6 space-y-4 animate-pulse">
      <div className="flex items-center gap-3">
        <LoadingSkeleton className="w-12 h-12 rounded-xl" />
        <div className="space-y-2 flex-1">
          <LoadingSkeleton className="h-5 w-32" />
          <LoadingSkeleton className="h-4 w-24" />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-3">
          <LoadingSkeleton className="h-4 w-full" />
          <LoadingSkeleton className="h-4 w-full" />
        </div>
        <div className="space-y-3">
          <LoadingSkeleton className="h-4 w-full" />
          <LoadingSkeleton className="h-4 w-full" />
        </div>
      </div>

      <div className="flex gap-2">
        <LoadingSkeleton className="h-9 flex-1" />
        <LoadingSkeleton className="h-9 w-16" />
        <LoadingSkeleton className="h-9 w-20" />
      </div>
    </div>
  )
}

export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="glass-ultra border border-border rounded-2xl p-6">
            <LoadingSkeleton className="h-4 w-24 mb-4" />
            <LoadingSkeleton className="h-8 w-16 mb-2" />
            <LoadingSkeleton className="h-3 w-20" />
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-3 gap-5">
        {Array.from({ length: 6 }).map((_, i) => (
          <ServerCardSkeleton key={i} />
        ))}
      </div>
    </div>
  )
}
