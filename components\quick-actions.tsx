"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Download, Upload, Settings, Users, Database } from 'lucide-react'

export default function QuickActions() {
  const actions = [
    { icon: Plus, label: "Create Server", color: "from-primary to-accent" },
    { icon: Users, label: "Manage Users", color: "from-purple-500 to-pink-500" },
    { icon: Database, label: "Backup Data", color: "from-emerald-500 to-cyan-500" },
    { icon: Settings, label: "System Config", color: "from-orange-500 to-red-500" },
    { icon: Download, label: "Download Logs", color: "from-blue-500 to-indigo-500" },
    { icon: Upload, label: "Upload Files", color: "from-green-500 to-teal-500" },
  ]

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground text-xl">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="h-16 flex-col space-y-2 glass-ultra border-border hover:bg-accent/5 transition-all duration-300 group"
            >
              <div className={`w-6 h-6 rounded-lg bg-gradient-to-br ${action.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                <action.icon className="w-3 h-3 text-white" />
              </div>
              <span className="text-xs text-muted-foreground group-hover:text-foreground transition-colors duration-300">
                {action.label}
              </span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
