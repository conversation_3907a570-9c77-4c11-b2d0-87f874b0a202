"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { QrCode, Shield, Github, Mail, LogIn } from 'lucide-react'
import { useState } from "react"
import { useToast } from "@/hooks/use-toast"

export default function SettingsSecurity() {
  const [twofa, setTwofa] = useState(false)
  const { toast } = useToast()

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader><CardTitle className="text-foreground">Security</CardTitle></CardHeader>
      <CardContent className="space-y-6">
        {/* Email verification banner (demo) */}
        <div className="p-4 rounded-xl border border-border/50 glass-ultra">
          <div className="flex items-center gap-3">
            <Shield className="w-4 h-4 text-yellow-400" />
            <div className="text-sm">
              <p className="text-foreground">Email not verified</p>
              <p className="text-muted-foreground">Please verify to enable full features.</p>
            </div>
            <Button size="sm" className="premium-button ml-auto" onClick={() => toast({ title: "Verification sent", description: "Check your inbox." })}>Send verification</Button>
          </div>
        </div>

        {/* 2FA */}
        <div className="p-4 rounded-xl border border-border/50 glass-ultra space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-foreground">Two-Factor Authentication (TOTP)</Label>
            <Switch checked={twofa} onCheckedChange={setTwofa} />
          </div>
          {twofa && (
            <div className="flex items-center gap-3">
              <div className="w-28 h-28 rounded-lg border border-border/50 flex items-center justify-center">
                <QrCode className="w-10 h-10 text-muted-foreground" />
              </div>
              <div className="space-y-2 flex-1">
                <Label className="text-foreground">Enter 6-digit code</Label>
                <Input placeholder="123 456" className="glass-ultra border-border max-w-xs" />
                <div><Button className="premium-button mt-2">Confirm</Button></div>
              </div>
            </div>
          )}
        </div>

        {/* Social Logins (UI only) */}
        <div className="p-4 rounded-xl border border-border/50 glass-ultra">
          <p className="text-sm text-foreground mb-3">Social Logins</p>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" className="secondary-button"><LogIn className="w-4 h-4 mr-2" />Discord</Button>
            <Button variant="outline" className="secondary-button"><Github className="w-4 h-4 mr-2" />GitHub</Button>
            <Button variant="outline" className="secondary-button"><Mail className="w-4 h-4 mr-2" />Google</Button>
          </div>
          <p className="text-xs text-muted-foreground mt-2">{'Connect your auth provider later.'}</p>
        </div>
      </CardContent>
    </Card>
  )
}
