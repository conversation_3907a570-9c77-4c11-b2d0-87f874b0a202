"use client"

import PanelLayout from "@/components/panel-layout"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Gift,
  Star,
  Users,
  Zap,
  Target,
  Award,
  Coins,
  Calendar,
  MessageSquare,
  Heart,
  Share2,
  Video,
  Camera,
  Music,
  Code,
  ExternalLink,
  CheckCircle,
  Timer,
  Flame,
  Trophy,
  Crown,
  Sparkles,
} from "lucide-react"
import { useWalletStore } from "@/stores/wallet-store"
import { useToast } from "@/hooks/use-toast"
import { useState } from "react"

interface Task {
  id: string
  title: string
  description: string
  reward: number
  icon: any
  category: string
  difficulty: "easy" | "medium" | "hard"
  timeEstimate: string
  completed?: boolean
  progress?: number
  maxProgress?: number
  platform?: string
  gradient: string
}

const tasks: Task[] = [
  // Daily Tasks
  {
    id: "daily-login",
    title: "Daily Login Bonus",
    description: "Log in to your account every day",
    reward: 10,
    icon: Calendar,
    category: "daily",
    difficulty: "easy",
    timeEstimate: "1 min",
    completed: false,
    gradient: "from-blue-500 to-cyan-600",
  },
  {
    id: "daily-server-check",
    title: "Check Server Status",
    description: "Visit your servers page",
    reward: 5,
    icon: Target,
    category: "daily",
    difficulty: "easy",
    timeEstimate: "30 sec",
    completed: true,
    gradient: "from-emerald-500 to-green-600",
  },
  {
    id: "daily-profile-update",
    title: "Update Profile",
    description: "Keep your profile information current",
    reward: 15,
    icon: Users,
    category: "daily",
    difficulty: "easy",
    timeEstimate: "2 min",
    completed: false,
    gradient: "from-purple-500 to-pink-600",
  },

  // Social Media Tasks
  {
    id: "discord-join",
    title: "Join Discord Server",
    description: "Join our official Discord community",
    reward: 50,
    icon: MessageSquare,
    category: "social",
    difficulty: "easy",
    timeEstimate: "2 min",
    platform: "Discord",
    gradient: "from-indigo-500 to-purple-600",
  },
  {
    id: "twitter-follow",
    title: "Follow on Twitter",
    description: "Follow @blockmanhosting for updates",
    reward: 25,
    icon: Share2,
    category: "social",
    difficulty: "easy",
    timeEstimate: "1 min",
    platform: "Twitter",
    gradient: "from-sky-500 to-blue-600",
  },
  {
    id: "youtube-subscribe",
    title: "Subscribe on YouTube",
    description: "Subscribe to our YouTube channel",
    reward: 30,
    icon: Video,
    category: "social",
    difficulty: "easy",
    timeEstimate: "1 min",
    platform: "YouTube",
    gradient: "from-red-500 to-red-600",
  },
  {
    id: "instagram-follow",
    title: "Follow on Instagram",
    description: "Follow us for behind-the-scenes content",
    reward: 20,
    icon: Camera,
    category: "social",
    difficulty: "easy",
    timeEstimate: "1 min",
    platform: "Instagram",
    gradient: "from-pink-500 to-rose-600",
  },
  {
    id: "tiktok-follow",
    title: "Follow on TikTok",
    description: "Follow for quick tips and updates",
    reward: 20,
    icon: Music,
    category: "social",
    difficulty: "easy",
    timeEstimate: "1 min",
    platform: "TikTok",
    gradient: "from-gray-800 to-gray-900",
  },
  {
    id: "github-star",
    title: "Star on GitHub",
    description: "Star our open-source projects",
    reward: 40,
    icon: Code,
    category: "social",
    difficulty: "easy",
    timeEstimate: "2 min",
    platform: "GitHub",
    gradient: "from-gray-700 to-gray-800",
  },

  // Offer Wall Tasks
  {
    id: "survey-complete",
    title: "Complete Survey",
    description: "Share your feedback in our user survey",
    reward: 100,
    icon: Award,
    category: "offers",
    difficulty: "medium",
    timeEstimate: "10 min",
    gradient: "from-amber-500 to-orange-600",
  },
  {
    id: "app-download",
    title: "Download Partner App",
    description: "Download and try our partner's mobile app",
    reward: 75,
    icon: ExternalLink,
    category: "offers",
    difficulty: "medium",
    timeEstimate: "5 min",
    gradient: "from-violet-500 to-purple-600",
  },
  {
    id: "newsletter-signup",
    title: "Newsletter Signup",
    description: "Subscribe to our weekly newsletter",
    reward: 35,
    icon: Heart,
    category: "offers",
    difficulty: "easy",
    timeEstimate: "2 min",
    gradient: "from-rose-500 to-pink-600",
  },
]

export default function EarnPage() {
  const { addCoins } = useWalletStore()
  const { toast } = useToast()
  const [completedTasks, setCompletedTasks] = useState<string[]>([])

  const dailyTasks = tasks.filter((task) => task.category === "daily")
  const socialTasks = tasks.filter((task) => task.category === "social")
  const offerTasks = tasks.filter((task) => task.category === "offers")

  const totalEarned = completedTasks.reduce((sum, taskId) => {
    const task = tasks.find((t) => t.id === taskId)
    return sum + (task?.reward || 0)
  }, 0)

  const dailyProgress =
    (dailyTasks.filter((task) => task.completed || completedTasks.includes(task.id)).length / dailyTasks.length) * 100
  const dailyStreak = 7 // Mock streak data

  const handleCompleteTask = (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId)
    if (!task || completedTasks.includes(taskId)) return

    setCompletedTasks((prev) => [...prev, taskId])
    addCoins(task.reward, `Completed: ${task.title}`)

    toast({
      title: "Task Completed! 🎉",
      description: `You earned ${task.reward} coins for "${task.title}"`,
    })
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "bg-emerald-400/10 text-emerald-400 border-emerald-400/20"
      case "medium":
        return "bg-amber-400/10 text-amber-400 border-amber-400/20"
      case "hard":
        return "bg-red-400/10 text-red-400 border-red-400/20"
      default:
        return "bg-gray-400/10 text-gray-400 border-gray-400/20"
    }
  }

  const TaskCard = ({ task }: { task: Task }) => {
    const isCompleted = task.completed || completedTasks.includes(task.id)
    const IconComponent = task.icon

    return (
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass hover:shadow-glass-lg transition-all duration-300 group">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div
              className={`w-12 h-12 rounded-xl bg-gradient-to-br ${task.gradient} flex items-center justify-center shadow-lg`}
            >
              <IconComponent className="w-6 h-6 text-white" />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h3 className="font-bold text-foreground truncate">{task.title}</h3>
                  <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Badge className={getDifficultyColor(task.difficulty)}>{task.difficulty}</Badge>
                  {task.platform && (
                    <Badge variant="outline" className="text-xs">
                      {task.platform}
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Timer className="w-4 h-4" />
                    {task.timeEstimate}
                  </div>
                  <div className="flex items-center gap-1">
                    <Coins className="w-4 h-4 text-amber-400" />
                    <span className="font-semibold text-foreground">{task.reward}</span>
                  </div>
                </div>

                {isCompleted ? (
                  <div className="flex items-center gap-2 text-emerald-400">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">Completed</span>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    onClick={() => handleCompleteTask(task.id)}
                    className="premium-button opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    Complete
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <PanelLayout title="Earn Coins" subtitle="Complete tasks and earn coins to upgrade your servers">
      <div className="space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)]/10 via-[rgb(32,141,209)]/8 to-[rgb(43,50,178)]/10 border border-[rgb(20,136,204)]/20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
          <div className="relative p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 border border-[rgb(20,136,204)]/30">
                  <Gift className="w-4 h-4 text-[rgb(20,136,204)]" />
                  <span className="text-sm font-medium text-foreground">Earn Rewards</span>
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-black brand-heading mb-4">Complete Tasks, Earn Coins</h1>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Earn coins by completing simple tasks like following our social media, joining our community, and
                    participating in surveys. Use coins to upgrade your servers and unlock premium features.
                  </p>
                </div>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Zap className="w-5 h-5 text-blue-400" />
                    <span className="text-sm font-medium">Instant Rewards</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Trophy className="w-5 h-5 text-amber-400" />
                    <span className="text-sm font-medium">Daily Bonuses</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Crown className="w-5 h-5 text-purple-400" />
                    <span className="text-sm font-medium">Premium Tasks</span>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-32 h-32 rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] shadow-2xl shadow-[rgb(20,136,204)]/25 mb-6">
                  <Gift className="w-16 h-16 text-white" />
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Total Earned Today</p>
                  <p className="text-5xl font-black text-foreground">{totalEarned}</p>
                  <p className="text-lg text-muted-foreground">coins</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20">
                  {Math.round(dailyProgress)}%
                </Badge>
              </div>
              <div className="space-y-2">
                <p className="text-2xl font-black text-foreground">{Math.round(dailyProgress)}%</p>
                <p className="text-sm text-muted-foreground">Daily Progress</p>
                <Progress value={dailyProgress} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
                  <Flame className="w-6 h-6 text-white" />
                </div>
                <Badge className="bg-amber-400/10 text-amber-400 border-amber-400/20">{dailyStreak} days</Badge>
              </div>
              <div>
                <p className="text-2xl font-black text-foreground">{dailyStreak}</p>
                <p className="text-sm text-muted-foreground">Day Streak</p>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">Available</Badge>
              </div>
              <div>
                <p className="text-2xl font-black text-foreground">{tasks.length - completedTasks.length}</p>
                <p className="text-sm text-muted-foreground">Tasks Remaining</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Daily Tasks */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              Daily Tasks
              <Badge className="bg-blue-400/10 text-blue-400 border-blue-400/20">Resets in 12h</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {dailyTasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </CardContent>
        </Card>

        {/* Social Media Tasks */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                <Share2 className="w-5 h-5 text-white" />
              </div>
              Social Media Tasks
              <Badge className="bg-indigo-400/10 text-indigo-400 border-indigo-400/20">One-time rewards</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {socialTasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </CardContent>
        </Card>

        {/* Offer Wall */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              Offer Wall
              <Badge className="bg-amber-400/10 text-amber-400 border-amber-400/20">High rewards</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {offerTasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </CardContent>
        </Card>
      </div>
    </PanelLayout>
  )
}
