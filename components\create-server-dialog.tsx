"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dialog<PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { useServerStore, type ServerType } from "@/stores/server-store"

type Props = {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function CreateServerDialog({ open, onOpenChange }: Props) {
  const create = useServerStore((s) => s.create)
  const [name, setName] = useState("")
  const [type, setType] = useState<ServerType>("Minecraft")
  const [memory, setMemory] = useState("2GB/4GB")
  const [cpu, setCpu] = useState("0%")

  const onSubmit = () => {
    if (!name.trim()) return
    const server = create({
      name,
      type,
      memory,
      cpu,
      status: "offline",
      players: "0/10",
      uptime: "Offline",
    })
    setName("")
    onOpenChange(false)
    // optional: navigate to server after creation
    // router.push(`/servers/${server.id}`)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild></DialogTrigger>
      <DialogContent className="glass-ultra border border-border shadow-glass">
        <DialogHeader>
          <DialogTitle className="text-foreground">Create Server</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-foreground">Server Name</Label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} placeholder="My Awesome Server" className="glass-ultra border-border" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label className="text-foreground">Type</Label>
              <Select value={type} onValueChange={(v) => setType(v as ServerType)}>
                <SelectTrigger className="glass-ultra border-border">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {["Minecraft", "Rust", "CS:GO", "Valheim", "ARK"].map((t) => (
                    <SelectItem key={t} value={t}>{t}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-foreground">Memory</Label>
              <Input value={memory} onChange={(e) => setMemory(e.target.value)} placeholder="2GB/4GB" className="glass-ultra border-border" />
            </div>

            <div className="space-y-2">
              <Label className="text-foreground">CPU</Label>
              <Input value={cpu} onChange={(e) => setCpu(e.target.value)} placeholder="0%" className="glass-ultra border-border" />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-2">
          <Button variant="outline" className="secondary-button" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button className="premium-button" onClick={onSubmit}>Create</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
