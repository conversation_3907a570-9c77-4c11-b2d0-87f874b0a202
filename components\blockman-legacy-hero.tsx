"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download } from 'lucide-react'

export default function BlockmanLegacyHero() {
  return (
    <section className="py-20 md:py-32 relative z-10">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 glass-ultra rounded-full px-4 py-2 mb-8 border border-white/20 shadow-glass">
            <span className="text-white font-medium text-sm">Our Current Project</span>
          </div>

          {/* App Icon */}
          <div className="relative w-32 h-32 md:w-40 md:h-40 mx-auto mb-8 rounded-3xl overflow-hidden border border-white/20 shadow-glass flex items-center justify-center bg-gradient-to-br from-[#780206] to-[#061161]">
            <Image
              src="/blockman-legacy-app-icon.png"
              alt="Blockman Legacy App Icon"
              width={160}
              height={160}
              className="object-cover w-full h-full p-2"
            />
          </div>

          {/* Title */}
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-black mb-6 leading-tight">
            <span className="block bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent enhanced-gradient">
              Blockman Legacy
            </span>
          </h2>

          {/* Description */}
          <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto font-light leading-relaxed">
            We're recreating the magic of Blockman Go's golden era (2020-2021) with love, nostalgia, and attention to every detail that made it special.
          </p>

          {/* CTA */}
          <Button size="lg" className="hero-button group">
            Download Beta
            <Download className="ml-3 w-5 h-5 group-hover:translate-y-1 transition-transform duration-300" />
          </Button>
        </div>
      </div>
    </section>
  )
}
