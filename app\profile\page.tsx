"use client"

import type React from "react"

import PanelLayout from "@/components/panel-layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { User, Mail, Shield, Calendar, Save, Edit, Camera } from "lucide-react"
import { useState } from "react"
import { useToast } from "@/hooks/use-toast"

export default function ProfilePage() {
  const { toast } = useToast()
  const [editing, setEditing] = useState(false)

  // Mock user data - replace with real user data
  const [profile, setProfile] = useState({
    name: "Admin User",
    email: "<EMAIL>",
    role: "Administrator",
    bio: "System administrator managing game servers and user accounts.",
    location: "San Francisco, CA",
    joinDate: "January 2024",
    avatar: null as string | null,
  })

  const handleSave = () => {
    setEditing(false)
    toast({ title: "Profile updated", description: "Your profile has been saved successfully." })
  }

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        setProfile((prev) => ({ ...prev, avatar: reader.result as string }))
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <PanelLayout title="Profile" subtitle="Manage your account information">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Profile Header */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              <div className="relative">
                <div className="w-24 h-24 rounded-2xl bg-brand flex items-center justify-center shadow-glass overflow-hidden">
                  {profile.avatar ? (
                    <img
                      src={profile.avatar || "/placeholder.svg"}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="w-12 h-12 text-white" />
                  )}
                </div>
                {editing && (
                  <label className="absolute -bottom-2 -right-2 w-8 h-8 bg-brand rounded-full flex items-center justify-center cursor-pointer shadow-lg hover:scale-110 transition-transform">
                    <Camera className="w-4 h-4 text-white" />
                    <input type="file" accept="image/*" className="hidden" onChange={handleAvatarChange} />
                  </label>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h1 className="text-2xl md:text-3xl font-black text-foreground">{profile.name}</h1>
                    <p className="text-muted-foreground flex items-center gap-2 mt-1">
                      <Mail className="w-4 h-4" />
                      {profile.email}
                    </p>
                    <div className="flex items-center gap-3 mt-2">
                      <Badge className="bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 text-foreground border border-border">
                        <Shield className="w-3 h-3 mr-1" />
                        {profile.role}
                      </Badge>
                      <span className="text-muted-foreground text-sm flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        Joined {profile.joinDate}
                      </span>
                    </div>
                  </div>

                  <Button
                    className={editing ? "premium-button" : "secondary-button bg-transparent"}
                    onClick={editing ? handleSave : () => setEditing(true)}
                  >
                    {editing ? (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </>
                    ) : (
                      <>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Profile
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Information */}
          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardHeader>
              <CardTitle className="text-foreground">Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-foreground">Full Name</Label>
                <Input
                  value={profile.name}
                  onChange={(e) => setProfile((prev) => ({ ...prev, name: e.target.value }))}
                  disabled={!editing}
                  className="glass-ultra border-border"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-foreground">Email Address</Label>
                <Input
                  value={profile.email}
                  onChange={(e) => setProfile((prev) => ({ ...prev, email: e.target.value }))}
                  disabled={!editing}
                  className="glass-ultra border-border"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-foreground">Location</Label>
                <Input
                  value={profile.location}
                  onChange={(e) => setProfile((prev) => ({ ...prev, location: e.target.value }))}
                  disabled={!editing}
                  className="glass-ultra border-border"
                  placeholder="City, Country"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-foreground">Bio</Label>
                <Textarea
                  value={profile.bio}
                  onChange={(e) => setProfile((prev) => ({ ...prev, bio: e.target.value }))}
                  disabled={!editing}
                  className="glass-ultra border-border"
                  placeholder="Tell us about yourself..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Account Stats */}
          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardHeader>
              <CardTitle className="text-foreground">Account Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 glass-ultra rounded-xl border border-border/50">
                  <p className="text-2xl font-black text-foreground">6</p>
                  <p className="text-muted-foreground text-sm">Servers Created</p>
                </div>
                <div className="text-center p-4 glass-ultra rounded-xl border border-border/50">
                  <p className="text-2xl font-black text-foreground">200</p>
                  <p className="text-muted-foreground text-sm">Coins Balance</p>
                </div>
                <div className="text-center p-4 glass-ultra rounded-xl border border-border/50">
                  <p className="text-2xl font-black text-foreground">15</p>
                  <p className="text-muted-foreground text-sm">Days Active</p>
                </div>
                <div className="text-center p-4 glass-ultra rounded-xl border border-border/50">
                  <p className="text-2xl font-black text-foreground">3</p>
                  <p className="text-muted-foreground text-sm">Referrals</p>
                </div>
              </div>

              <div className="space-y-3 pt-4 border-t border-border/50">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Account Status</span>
                  <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20">Active</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Email Verified</span>
                  <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20">Verified</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">2FA Enabled</span>
                  <Badge className="bg-amber-400/10 text-amber-400 border-amber-400/20">Disabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PanelLayout>
  )
}
