"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChevronDown, LogOut, Settings, User, Mail, Shield } from "lucide-react"
import { useRouter } from "next/navigation"

export default function UserMenu() {
  const router = useRouter()

  // Replace with your auth user
  const name = "Admin User"
  const email = "<EMAIL>"
  const role = "Administrator"

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="glass-button pl-1 pr-2 h-9" aria-label="Open user menu">
          <div className="w-7 h-7 rounded-full bg-brand mr-2 border border-border shadow-glass" />
          <span className="hidden md:inline text-sm text-foreground">{name}</span>
          <ChevronDown className="w-4 h-4 ml-1 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-72 glass-ultra border border-border shadow-glass">
        {/* Profile Section */}
        <DropdownMenuLabel className="p-0">
          <div className="flex items-center gap-3 p-3">
            <div className="w-12 h-12 rounded-full bg-brand border border-border shadow-glass flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-foreground font-semibold truncate">{name}</p>
              <p className="text-xs text-muted-foreground truncate flex items-center gap-1">
                <Mail className="w-3 h-3" />
                {email}
              </p>
              <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                <Shield className="w-3 h-3" />
                {role}
              </p>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem className="gap-2" onClick={() => router.push("/profile")}>
          <User className="w-4 h-4" />
          View Profile
        </DropdownMenuItem>

        <DropdownMenuItem className="gap-2" onClick={() => router.push("/settings")}>
          <Settings className="w-4 h-4" />
          Settings
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem className="gap-2 text-red-400 focus:text-red-300">
          <LogOut className="w-4 h-4" />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
