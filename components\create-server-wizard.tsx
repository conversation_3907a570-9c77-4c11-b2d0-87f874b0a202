"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import {
  Server,
  Bot,
  Globe,
  Database,
  Gamepad2,
  Cpu,
  HardDrive,
  Zap,
  MapPin,
  Star,
  Check,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Crown,
  Shield,
  Rocket,
  Coffee,
  Hammer,
  Pickaxe,
  Sword,
  Wand2,
  Package,
  Code,
  Terminal,
} from "lucide-react"
import { useServerStore } from "@/stores/server-store"
import { useToast } from "@/hooks/use-toast"

interface ServerPlan {
  id: string
  name: string
  cpu: string
  ram: string
  storage: string
  price: number
  popular?: boolean
  premium?: boolean
}

interface ServerType {
  id: string
  name: string
  description: string
  icon: any
  gradient: string
  eggs: ServerEgg[]
}

interface Server<PERSON>gg {
  id: string
  name: string
  description: string
  icon: any
  version?: string
  popular?: boolean
}

interface Location {
  id: string
  name: string
  country: string
  flag: string
  ping: number
  popular?: boolean
}

const serverTypes: ServerType[] = [
  {
    id: "minecraft",
    name: "Minecraft",
    description: "Java & Bedrock Edition servers",
    icon: Pickaxe,
    gradient: "from-green-500 to-emerald-600",
    eggs: [
      { id: "paper", name: "Paper", description: "High performance Spigot fork", icon: Coffee, popular: true },
      { id: "spigot", name: "Spigot", description: "Modified Minecraft server", icon: Hammer },
      { id: "forge", name: "Forge", description: "Modded Minecraft server", icon: Hammer },
      { id: "fabric", name: "Fabric", description: "Lightweight modding platform", icon: Package },
      { id: "vanilla", name: "Vanilla", description: "Official Minecraft server", icon: Pickaxe },
      { id: "bedrock", name: "Bedrock", description: "Pocket Edition server", icon: Shield },
    ],
  },
  {
    id: "discord-bot",
    name: "Discord Bot",
    description: "Host your Discord bots 24/7",
    icon: Bot,
    gradient: "from-indigo-500 to-purple-600",
    eggs: [
      { id: "nodejs", name: "Node.js", description: "JavaScript runtime", icon: Code, popular: true },
      { id: "python", name: "Python", description: "Python bot hosting", icon: Terminal },
      { id: "java", name: "Java", description: "JDA Discord bots", icon: Coffee },
      { id: "csharp", name: "C#", description: "Discord.NET bots", icon: Code },
    ],
  },
  {
    id: "web-hosting",
    name: "Web Hosting",
    description: "Host websites and web applications",
    icon: Globe,
    gradient: "from-blue-500 to-cyan-600",
    eggs: [
      { id: "nodejs-web", name: "Node.js", description: "Express, React, Next.js", icon: Code, popular: true },
      { id: "php", name: "PHP", description: "WordPress, Laravel", icon: Code },
      { id: "python-web", name: "Python", description: "Django, Flask", icon: Terminal },
      { id: "static", name: "Static", description: "HTML, CSS, JS", icon: Globe },
    ],
  },
  {
    id: "database",
    name: "Database",
    description: "MySQL, PostgreSQL, MongoDB",
    icon: Database,
    gradient: "from-orange-500 to-red-600",
    eggs: [
      { id: "mysql", name: "MySQL", description: "Popular SQL database", icon: Database, popular: true },
      { id: "postgresql", name: "PostgreSQL", description: "Advanced SQL database", icon: Database },
      { id: "mongodb", name: "MongoDB", description: "NoSQL document database", icon: Database },
      { id: "redis", name: "Redis", description: "In-memory data store", icon: Zap },
    ],
  },
  {
    id: "game-servers",
    name: "Game Servers",
    description: "Various game server hosting",
    icon: Gamepad2,
    gradient: "from-purple-500 to-pink-600",
    eggs: [
      { id: "csgo", name: "CS:GO", description: "Counter-Strike server", icon: Sword, popular: true },
      { id: "gmod", name: "Garry's Mod", description: "GMod server hosting", icon: Wand2 },
      { id: "rust", name: "Rust", description: "Survival game server", icon: Shield },
      { id: "ark", name: "ARK", description: "Survival Evolved server", icon: Gamepad2 },
    ],
  },
]

const serverPlans: ServerPlan[] = [
  {
    id: "starter",
    name: "Starter",
    cpu: "1 vCPU",
    ram: "1 GB",
    storage: "10 GB SSD",
    price: 0,
  },
  {
    id: "basic",
    name: "Basic",
    cpu: "1 vCPU",
    ram: "2 GB",
    storage: "20 GB SSD",
    price: 50,
    popular: true,
  },
  {
    id: "standard",
    name: "Standard",
    cpu: "2 vCPU",
    ram: "4 GB",
    storage: "40 GB SSD",
    price: 100,
  },
  {
    id: "premium",
    name: "Premium",
    cpu: "4 vCPU",
    ram: "8 GB",
    storage: "80 GB SSD",
    price: 200,
    premium: true,
  },
  {
    id: "ultimate",
    name: "Ultimate",
    cpu: "8 vCPU",
    ram: "16 GB",
    storage: "160 GB SSD",
    price: 400,
    premium: true,
  },
]

const locations: Location[] = [
  { id: "us-east", name: "New York", country: "United States", flag: "🇺🇸", ping: 25, popular: true },
  { id: "us-west", name: "Los Angeles", country: "United States", flag: "🇺🇸", ping: 30 },
  { id: "eu-west", name: "London", country: "United Kingdom", flag: "🇬🇧", ping: 15, popular: true },
  { id: "eu-central", name: "Frankfurt", country: "Germany", flag: "🇩🇪", ping: 20 },
  { id: "asia-east", name: "Singapore", country: "Singapore", flag: "🇸🇬", ping: 45 },
  { id: "asia-northeast", name: "Tokyo", country: "Japan", flag: "🇯🇵", ping: 50 },
]

export default function CreateServerWizard() {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedType, setSelectedType] = useState<ServerType | null>(null)
  const [selectedEgg, setSelectedEgg] = useState<ServerEgg | null>(null)
  const [selectedPlan, setSelectedPlan] = useState<ServerPlan | null>(null)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [serverName, setServerName] = useState("")
  const [serverDescription, setServerDescription] = useState("")

  const { addServer } = useServerStore()
  const { toast } = useToast()

  const totalSteps = 4
  const progress = (currentStep / totalSteps) * 100

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleCreateServer = () => {
    if (!selectedType || !selectedEgg || !selectedPlan || !selectedLocation || !serverName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    const newServer = {
      id: Date.now().toString(),
      name: serverName,
      description: serverDescription,
      type: selectedType.name,
      egg: selectedEgg.name,
      plan: selectedPlan.name,
      location: selectedLocation.name,
      status: "creating" as const,
      cpu: 0,
      ram: 0,
      disk: 0,
      players: 0,
      maxPlayers: 20,
      uptime: "0m",
      version: selectedEgg.version || "Latest",
    }

    addServer(newServer)
    toast({
      title: "Server Created! 🎉",
      description: `${serverName} is being set up. This may take a few minutes.`,
    })

    // Reset form
    setCurrentStep(1)
    setSelectedType(null)
    setSelectedEgg(null)
    setSelectedPlan(null)
    setSelectedLocation(null)
    setServerName("")
    setServerDescription("")
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return selectedType !== null
      case 2:
        return selectedEgg !== null
      case 3:
        return selectedPlan !== null
      case 4:
        return selectedLocation !== null && serverName.trim() !== ""
      default:
        return false
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 border border-[rgb(20,136,204)]/30">
          <Rocket className="w-4 h-4 text-[rgb(20,136,204)]" />
          <span className="text-sm font-medium text-foreground">Server Creation</span>
        </div>
        <h1 className="text-3xl md:text-4xl font-black brand-heading">Create New Server</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Set up your perfect server in just a few steps. Choose from various types, plans, and locations.
        </p>
      </div>

      {/* Progress */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-foreground">
              Step {currentStep} of {totalSteps}
            </span>
            <span className="text-sm text-muted-foreground">{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between mt-4 text-xs text-muted-foreground">
            <span className={currentStep >= 1 ? "text-[rgb(20,136,204)]" : ""}>Server Type</span>
            <span className={currentStep >= 2 ? "text-[rgb(20,136,204)]" : ""}>Software</span>
            <span className={currentStep >= 3 ? "text-[rgb(20,136,204)]" : ""}>Plan</span>
            <span className={currentStep >= 4 ? "text-[rgb(20,136,204)]" : ""}>Details</span>
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <div className="min-h-[600px]">
        {/* Step 1: Server Type */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Choose Server Type</h2>
              <p className="text-muted-foreground">What kind of server would you like to create?</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {serverTypes.map((type) => {
                const IconComponent = type.icon
                const isSelected = selectedType?.id === type.id

                return (
                  <Card
                    key={type.id}
                    className={`glass-ultra border rounded-2xl shadow-glass cursor-pointer transition-all duration-300 hover:scale-105 ${
                      isSelected
                        ? "border-[rgb(20,136,204)] bg-[rgb(20,136,204)]/5"
                        : "border-border hover:border-[rgb(20,136,204)]/50"
                    }`}
                    onClick={() => setSelectedType(type)}
                  >
                    <CardContent className="p-6 text-center">
                      <div
                        className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${type.gradient} flex items-center justify-center shadow-lg`}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-lg font-bold text-foreground mb-2">{type.name}</h3>
                      <p className="text-sm text-muted-foreground mb-4">{type.description}</p>
                      {isSelected && (
                        <div className="flex items-center justify-center gap-2 text-[rgb(20,136,204)]">
                          <Check className="w-4 h-4" />
                          <span className="text-sm font-medium">Selected</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* Step 2: Egg Selection */}
        {currentStep === 2 && selectedType && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Choose Software</h2>
              <p className="text-muted-foreground">Select the software for your {selectedType.name} server</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {selectedType.eggs.map((egg) => {
                const IconComponent = egg.icon
                const isSelected = selectedEgg?.id === egg.id

                return (
                  <Card
                    key={egg.id}
                    className={`glass-ultra border rounded-2xl shadow-glass cursor-pointer transition-all duration-300 hover:scale-105 ${
                      isSelected
                        ? "border-[rgb(20,136,204)] bg-[rgb(20,136,204)]/5"
                        : "border-border hover:border-[rgb(20,136,204)]/50"
                    }`}
                    onClick={() => setSelectedEgg(egg)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div
                          className={`w-12 h-12 rounded-xl bg-gradient-to-br ${selectedType.gradient} flex items-center justify-center shadow-lg`}
                        >
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="text-lg font-bold text-foreground">{egg.name}</h3>
                            {egg.popular && (
                              <Badge className="bg-amber-400/10 text-amber-400 border-amber-400/20">
                                <Star className="w-3 h-3 mr-1" />
                                Popular
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mb-3">{egg.description}</p>
                          {egg.version && (
                            <Badge variant="outline" className="text-xs">
                              {egg.version}
                            </Badge>
                          )}
                          {isSelected && (
                            <div className="flex items-center gap-2 text-[rgb(20,136,204)] mt-3">
                              <Check className="w-4 h-4" />
                              <span className="text-sm font-medium">Selected</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* Step 3: Plan Selection */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Choose Plan</h2>
              <p className="text-muted-foreground">Select the resources for your server</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
              {serverPlans.map((plan) => {
                const isSelected = selectedPlan?.id === plan.id

                return (
                  <Card
                    key={plan.id}
                    className={`glass-ultra border rounded-2xl shadow-glass cursor-pointer transition-all duration-300 hover:scale-105 relative ${
                      isSelected
                        ? "border-[rgb(20,136,204)] bg-[rgb(20,136,204)]/5"
                        : "border-border hover:border-[rgb(20,136,204)]/50"
                    }`}
                    onClick={() => setSelectedPlan(plan)}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] text-white border-0">
                          <Star className="w-3 h-3 mr-1" />
                          Popular
                        </Badge>
                      </div>
                    )}
                    {plan.premium && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0">
                          <Crown className="w-3 h-3 mr-1" />
                          Premium
                        </Badge>
                      </div>
                    )}

                    <CardContent className="p-6 text-center">
                      <h3 className="text-lg font-bold text-foreground mb-4">{plan.name}</h3>

                      <div className="space-y-3 mb-6">
                        <div className="flex items-center gap-2 text-sm">
                          <Cpu className="w-4 h-4 text-blue-400" />
                          <span className="text-muted-foreground">{plan.cpu}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Zap className="w-4 h-4 text-emerald-400" />
                          <span className="text-muted-foreground">{plan.ram}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <HardDrive className="w-4 h-4 text-purple-400" />
                          <span className="text-muted-foreground">{plan.storage}</span>
                        </div>
                      </div>

                      <div className="mb-4">
                        <div className="text-3xl font-black text-foreground mb-1">
                          {plan.price === 0 ? "Free" : `${plan.price}`}
                        </div>
                        {plan.price > 0 && <div className="text-sm text-muted-foreground">coins/month</div>}
                      </div>

                      {isSelected && (
                        <div className="flex items-center justify-center gap-2 text-[rgb(20,136,204)]">
                          <Check className="w-4 h-4" />
                          <span className="text-sm font-medium">Selected</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* Step 4: Server Details */}
        {currentStep === 4 && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Server Details</h2>
              <p className="text-muted-foreground">Configure your server settings and location</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Server Configuration */}
              <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                      <Server className="w-5 h-5 text-white" />
                    </div>
                    Server Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="serverName">Server Name *</Label>
                    <Input
                      id="serverName"
                      placeholder="My Awesome Server"
                      value={serverName}
                      onChange={(e) => setServerName(e.target.value)}
                      className="glass-ultra border-border"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="serverDescription">Description (Optional)</Label>
                    <Input
                      id="serverDescription"
                      placeholder="A brief description of your server"
                      value={serverDescription}
                      onChange={(e) => setServerDescription(e.target.value)}
                      className="glass-ultra border-border"
                    />
                  </div>

                  <div className="space-y-4">
                    <Label>Server Location *</Label>
                    <div className="grid grid-cols-1 gap-3">
                      {locations.map((location) => (
                        <Card
                          key={location.id}
                          className={`glass-ultra border cursor-pointer transition-all duration-200 ${
                            selectedLocation?.id === location.id
                              ? "border-[rgb(20,136,204)] bg-[rgb(20,136,204)]/5"
                              : "border-border hover:border-[rgb(20,136,204)]/50"
                          }`}
                          onClick={() => setSelectedLocation(location)}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <span className="text-2xl">{location.flag}</span>
                                <div>
                                  <p className="font-semibold text-foreground">{location.name}</p>
                                  <p className="text-sm text-muted-foreground">{location.country}</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <MapPin className="w-4 h-4" />
                                  {location.ping}ms
                                </div>
                                {location.popular && (
                                  <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20">
                                    Popular
                                  </Badge>
                                )}
                                {selectedLocation?.id === location.id && (
                                  <Check className="w-5 h-5 text-[rgb(20,136,204)]" />
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Order Summary */}
              <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                      <Sparkles className="w-5 h-5 text-white" />
                    </div>
                    Order Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {selectedType && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Server Type</span>
                      <span className="font-semibold text-foreground">{selectedType.name}</span>
                    </div>
                  )}

                  {selectedEgg && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Software</span>
                      <span className="font-semibold text-foreground">{selectedEgg.name}</span>
                    </div>
                  )}

                  {selectedPlan && (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Plan</span>
                        <span className="font-semibold text-foreground">{selectedPlan.name}</span>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">CPU</span>
                          <span className="text-foreground">{selectedPlan.cpu}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">RAM</span>
                          <span className="text-foreground">{selectedPlan.ram}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Storage</span>
                          <span className="text-foreground">{selectedPlan.storage}</span>
                        </div>
                      </div>
                    </>
                  )}

                  {selectedLocation && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Location</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{selectedLocation.flag}</span>
                        <span className="font-semibold text-foreground">{selectedLocation.name}</span>
                      </div>
                    </div>
                  )}

                  <Separator />

                  <div className="flex items-center justify-between text-lg font-bold">
                    <span className="text-foreground">Total Cost</span>
                    <span className="text-foreground">
                      {selectedPlan?.price === 0 ? "Free" : `${selectedPlan?.price || 0} coins/month`}
                    </span>
                  </div>

                  {serverName && (
                    <div className="p-4 glass-ultra rounded-xl border border-border/50">
                      <p className="text-sm text-muted-foreground mb-1">Server Name</p>
                      <p className="font-semibold text-foreground">{serverName}</p>
                      {serverDescription && (
                        <>
                          <p className="text-sm text-muted-foreground mb-1 mt-2">Description</p>
                          <p className="text-sm text-foreground">{serverDescription}</p>
                        </>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* Navigation */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="secondary-button bg-transparent"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <div className="flex items-center gap-4">
              {currentStep < totalSteps ? (
                <Button onClick={handleNext} disabled={!canProceed()} className="premium-button">
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button onClick={handleCreateServer} disabled={!canProceed()} className="premium-button">
                  <Rocket className="w-4 h-4 mr-2" />
                  Create Server
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
