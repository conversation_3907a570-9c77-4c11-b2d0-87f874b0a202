"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Folder, File, Download, Upload, Edit, Trash2, Plus, ArrowLeft } from 'lucide-react'

interface ServerFilesProps {
  server: any
}

export default function ServerFiles({ server }: ServerFilesProps) {
  const [currentPath, setCurrentPath] = useState("/")
  const [files] = useState([
    { name: "plugins", type: "folder", size: "-", modified: "2024-01-15" },
    { name: "world", type: "folder", size: "-", modified: "2024-01-14" },
    { name: "logs", type: "folder", size: "-", modified: "2024-01-16" },
    { name: "server.properties", type: "file", size: "2.1 KB", modified: "2024-01-10" },
    { name: "whitelist.json", type: "file", size: "156 B", modified: "2024-01-12" },
    { name: "banned-players.json", type: "file", size: "89 B", modified: "2024-01-08" },
    { name: "ops.json", type: "file", size: "234 B", modified: "2024-01-05" },
  ])

  return (
    <Card className="glass-ultra border border-border shadow-glass">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-foreground">File Manager</CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="secondary-button">
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </Button>
            <Button variant="outline" size="sm" className="secondary-button">
              <Plus className="w-4 h-4 mr-2" />
              New Folder
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Path Navigation */}
        <div className="flex items-center space-x-2 p-3 glass-ultra rounded-xl border border-border/50">
          <Button variant="ghost" size="sm" className="glass-button p-2">
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <span className="text-foreground font-mono text-sm">{currentPath}</span>
        </div>

        {/* File List */}
        <div className="space-y-2">
          {files.map((file, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 glass-ultra rounded-xl border border-border/50 hover:bg-accent/5 transition-colors cursor-pointer group"
            >
              <div className="flex items-center space-x-3">
                {file.type === 'folder' ? (
                  <Folder className="w-5 h-5 text-blue-400" />
                ) : (
                  <File className="w-5 h-5 text-muted-foreground" />
                )}
                <div>
                  <p className="text-foreground font-medium">{file.name}</p>
                  <p className="text-muted-foreground text-xs">
                    {file.size} • Modified {file.modified}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button variant="ghost" size="sm" className="glass-button p-2">
                  <Download className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="glass-button p-2">
                  <Edit className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="glass-button p-2 text-red-400 hover:text-red-300">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
