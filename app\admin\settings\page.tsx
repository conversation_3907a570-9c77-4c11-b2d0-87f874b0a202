"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Save, Server, Mail, Shield } from "lucide-react"

export default function AdminSettingsPage() {
  return (
    <AdminLayout title="System Settings" subtitle="Configure panel-wide settings and preferences">
      <div className="space-y-6">
        {/* General Settings */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-foreground flex items-center gap-2">
              <Server className="w-5 h-5" />
              General Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-foreground">Panel Name</Label>
                <Input defaultValue="Pterodash" className="glass-ultra border-border" />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">Panel URL</Label>
                <Input defaultValue="https://panel.example.com" className="glass-ultra border-border" />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-foreground">Welcome Message</Label>
              <Textarea
                defaultValue="Welcome to Pterodash! Manage your game servers with ease."
                className="glass-ultra border-border"
                rows={3}
              />
            </div>

            <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
              <div>
                <Label className="text-foreground font-medium">Maintenance Mode</Label>
                <p className="text-muted-foreground text-sm">Temporarily disable access to the panel</p>
              </div>
              <Switch />
            </div>

            <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
              <div>
                <Label className="text-foreground font-medium">User Registration</Label>
                <p className="text-muted-foreground text-sm">Allow new users to register accounts</p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Email Settings */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-foreground flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Email Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-foreground">SMTP Host</Label>
                <Input placeholder="smtp.gmail.com" className="glass-ultra border-border" />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">SMTP Port</Label>
                <Input placeholder="587" className="glass-ultra border-border" />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-foreground">Username</Label>
                <Input placeholder="<EMAIL>" className="glass-ultra border-border" />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">Password</Label>
                <Input type="password" placeholder="••••••••" className="glass-ultra border-border" />
              </div>
            </div>

            <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
              <div>
                <Label className="text-foreground font-medium">Enable Email Notifications</Label>
                <p className="text-muted-foreground text-sm">Send system notifications via email</p>
              </div>
              <Switch defaultChecked />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-foreground flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Security Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-foreground">Session Timeout (minutes)</Label>
                <Input defaultValue="60" type="number" className="glass-ultra border-border" />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">Max Login Attempts</Label>
                <Input defaultValue="5" type="number" className="glass-ultra border-border" />
              </div>
            </div>

            <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
              <div>
                <Label className="text-foreground font-medium">Require 2FA for Admins</Label>
                <p className="text-muted-foreground text-sm">Force two-factor authentication for admin accounts</p>
              </div>
              <Switch />
            </div>

            <div className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
              <div>
                <Label className="text-foreground font-medium">IP Whitelist Mode</Label>
                <p className="text-muted-foreground text-sm">Only allow access from whitelisted IP addresses</p>
              </div>
              <Switch />
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button className="premium-button">
            <Save className="w-4 h-4 mr-2" />
            Save All Settings
          </Button>
        </div>
      </div>
    </AdminLayout>
  )
}
