"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { User, Shield, Ban, MessageSquare, Search } from 'lucide-react'

interface ServerPlayersProps {
  server: any
}

export default function ServerPlayers({ server }: ServerPlayersProps) {
  const players = [
    { name: "<PERSON>", status: "online", playtime: "2h 34m", lastSeen: "Now", role: "Player" },
    { name: "<PERSON>", status: "online", playtime: "1h 15m", lastSeen: "Now", role: "Moderator" },
    { name: "<PERSON>", status: "online", playtime: "45m", lastSeen: "Now", role: "Player" },
    { name: "<PERSON>", status: "offline", playtime: "5h 22m", lastSeen: "2 hours ago", role: "Player" },
    { name: "<PERSON>", status: "offline", playtime: "12h 8m", lastSeen: "1 day ago", role: "Admin" },
  ]

  return (
    <Card className="glass-ultra border border-border shadow-glass">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <CardTitle className="text-foreground">Player Management</CardTitle>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search players..."
                className="pl-10 glass-ultra border-border text-foreground placeholder:text-muted-foreground"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {players.map((player, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50 hover:bg-accent/5 transition-colors group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                  <User className="w-5 h-5 text-primary-foreground" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <p className="text-foreground font-medium">{player.name}</p>
                    <div className={`w-2 h-2 rounded-full ${
                      player.status === 'online' ? 'bg-emerald-400' : 'bg-gray-400'
                    }`}></div>
                    <span className="text-xs px-2 py-1 rounded-full bg-accent/20 text-accent-foreground">
                      {player.role}
                    </span>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    Playtime: {player.playtime} • Last seen: {player.lastSeen}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button variant="ghost" size="sm" className="glass-button p-2">
                  <MessageSquare className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="glass-button p-2">
                  <Shield className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="glass-button p-2 text-red-400 hover:text-red-300">
                  <Ban className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
