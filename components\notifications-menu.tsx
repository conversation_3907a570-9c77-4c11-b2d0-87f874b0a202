"use client"

import { <PERSON>, <PERSON>, CircleDot, Trash2 } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useNotificationStore } from "@/stores/notification-store"

export default function NotificationsMenu() {
  const { notifications, markAllRead, clear } = useNotificationStore()

  const unread = notifications.filter(n => !n.read).length

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="glass-button p-2 relative">
          <Bell className="w-4 h-4" />
          {unread > 0 && <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-[10px] rounded-full flex items-center justify-center text-white">{unread}</div>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 glass-ultra border border-border shadow-glass">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="h-7 w-7" onClick={markAllRead} title="Mark all read">
              <Check className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-7 w-7 text-red-400 hover:text-red-300" onClick={clear} title="Clear all">
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {notifications.length === 0 ? (
          <DropdownMenuItem className="text-muted-foreground">No notifications yet</DropdownMenuItem>
        ) : (
          notifications.slice(0, 8).map((n) => (
            <DropdownMenuItem key={n.id} className="flex items-start gap-2 whitespace-normal">
              <CircleDot className={`w-3 h-3 mt-1 ${n.read ? "text-muted-foreground" : "text-emerald-400"}`} />
              <div>
                <p className="text-sm text-foreground">{n.title}</p>
                <p className="text-xs text-muted-foreground">{n.message}</p>
              </div>
            </DropdownMenuItem>
          ))
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
