"use client"

import { useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import {
  Sidebar,
  SidebarContent as SidebarComponent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { LayoutDashboard, Server, Database, Settings, Shield, Activity, FileText, Wallet, Gift, Users, Gamepad2, CreditCard, UserPlus, Code, ChevronRight, Menu, X } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { UserMenu } from "@/components/user-menu"

const mainNavItems = [
  {
    title: "Dashboard",
    url: "/",
    icon: LayoutDashboard,
  },
  {
    title: "Servers",
    url: "/servers",
    icon: Server,
  },
]

const accountNavItems = [
  {
    title: "Wallet",
    url: "/wallet",
    icon: Wallet,
  },
  {
    title: "Earn Coins",
    url: "/earn",
    icon: Gift,
  },
  {
    title: "Referrals",
    url: "/referrals",
    icon: UserPlus,
  },
  {
    title: "Redeem Code",
    url: "/redeem",
    icon: Code,
  },
  {
    title: "Transfer Coins",
    url: "/transfer",
    icon: CreditCard,
  },
]

function PterodactylSidebarContent() {
  const pathname = usePathname()
  const { state } = useSidebar()

  return (
    <>
      <SidebarHeader className="border-b border-border/50 pb-4">
        <div className="flex items-center gap-3 px-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Gamepad2 className="w-4 h-4 text-white" />
          </div>
          {state === "expanded" && (
            <div>
              <h2 className="text-lg font-bold text-foreground">Pterodash</h2>
              <p className="text-xs text-muted-foreground">Game Panel</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarComponent>
        <SidebarGroup>
          <SidebarGroupLabel>Main</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainNavItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.url}
                    tooltip={state === "collapsed" ? item.title : undefined}
                  >
                    <Link href={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Account</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {accountNavItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.url}
                    tooltip={state === "collapsed" ? item.title : undefined}
                  >
                    <Link href={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarComponent>

      <SidebarFooter className="border-t border-border/50 pt-4">
        <UserMenu />
      </SidebarFooter>
    </>
  )
}

export function PterodactylSidebar() {
  return (
    <Sidebar variant="sidebar" collapsible="icon">
      <PterodactylSidebarContent />
    </Sidebar>
  )
}

export function PterodactylSidebarProvider({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen w-full">
        <PterodactylSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          {children}
        </div>
      </div>
    </SidebarProvider>
  )
}
