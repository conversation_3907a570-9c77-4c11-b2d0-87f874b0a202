"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Send, Download, Trash2 } from 'lucide-react'

interface ServerConsoleProps {
  server: any
}

export default function ServerConsole({ server }: ServerConsoleProps) {
  const [command, setCommand] = useState("")
  const [logs, setLogs] = useState([
    { time: "14:32:15", type: "info", message: "Server started successfully" },
    { time: "14:32:16", type: "info", message: "Loading world..." },
    { time: "14:32:18", type: "info", message: "World loaded successfully" },
    { time: "14:32:20", type: "info", message: "Player '<PERSON>' joined the game" },
    { time: "14:33:45", type: "warn", message: "Player '<PERSON>' tried to use unknown command" },
    { time: "14:34:12", type: "info", message: "Player '<PERSON>' joined the game" },
    { time: "14:35:30", type: "error", message: "Connection timeout for player '<PERSON>'" },
    { time: "14:36:15", type: "info", message: "Autosave completed" },
  ])
  const consoleRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight
    }
  }, [logs])

  const handleSendCommand = () => {
    if (command.trim()) {
      const newLog = {
        time: new Date().toLocaleTimeString('en-US', { hour12: false }),
        type: "command",
        message: `> ${command}`
      }
      setLogs(prev => [...prev, newLog])
      setCommand("")
      
      // Simulate command response
      setTimeout(() => {
        const response = {
          time: new Date().toLocaleTimeString('en-US', { hour12: false }),
          type: "info",
          message: `Command executed: ${command}`
        }
        setLogs(prev => [...prev, response])
      }, 500)
    }
  }

  const getLogColor = (type: string) => {
    switch (type) {
      case 'error': return 'text-red-400'
      case 'warn': return 'text-yellow-400'
      case 'command': return 'text-blue-400'
      default: return 'text-foreground'
    }
  }

  return (
    <Card className="glass-ultra border border-border shadow-glass">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-foreground">Server Console</CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="secondary-button">
              <Download className="w-4 h-4 mr-2" />
              Download Logs
            </Button>
            <Button variant="outline" size="sm" className="secondary-button">
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Console Output */}
        <div 
          ref={consoleRef}
          className="bg-black/50 rounded-xl p-4 h-96 overflow-y-auto font-mono text-sm border border-border/50"
        >
          {logs.map((log, index) => (
            <div key={index} className="flex space-x-3 mb-1">
              <span className="text-muted-foreground text-xs w-20 flex-shrink-0">
                [{log.time}]
              </span>
              <span className={`${getLogColor(log.type)} break-all`}>
                {log.message}
              </span>
            </div>
          ))}
        </div>

        {/* Command Input */}
        <div className="flex space-x-2">
          <Input
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendCommand()}
            placeholder="Enter server command..."
            className="flex-1 glass-ultra border-border text-foreground placeholder:text-muted-foreground focus:border-primary/50 font-mono"
          />
          <Button onClick={handleSendCommand} className="premium-button">
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
