"use client"

import { useRouter } from "next/navigation"
import { useServerStore } from "@/stores/server-store"
import ServerGrid from "@/components/server-grid"
import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle } from 'lucide-react'

export default function ServersGridPage() {
  const router = useRouter()
  const servers = useServerStore((s) => s.servers)

  if (!servers.length) {
    return (
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardContent className="p-8 text-center text-muted-foreground">
          <div className="flex items-center justify-center mb-3">
            <AlertTriangle className="w-5 h-5" />
          </div>
          <p>{"No servers yet. Click 'Create Server' to get started."}</p>
        </CardContent>
      </Card>
    )
  }

  return <ServerGrid onServerSelect={(server) => router.push(`/servers/${server.id}`)} />
}
