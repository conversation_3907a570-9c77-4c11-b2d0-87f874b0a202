"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Cpu, HardDrive, Activity, Zap } from 'lucide-react'

export default function ResourceUsage() {
  const resources = [
    {
      name: "CPU Usage",
      value: 34,
      max: 100,
      unit: "%",
      icon: Cpu,
      color: "from-blue-500 to-cyan-500"
    },
    {
      name: "Memory Usage",
      value: 12.4,
      max: 32,
      unit: "GB",
      icon: HardDrive,
      color: "from-purple-500 to-pink-500"
    },
    {
      name: "Network I/O",
      value: 1.2,
      max: 10,
      unit: "GB/s",
      icon: Activity,
      color: "from-emerald-500 to-green-500"
    },
    {
      name: "Power Usage",
      value: 450,
      max: 1000,
      unit: "W",
      icon: Zap,
      color: "from-orange-500 to-red-500"
    },
  ]

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground text-xl">Resource Usage</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {resources.map((resource, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <resource.icon className="w-4 h-4 text-muted-foreground" />
                  <span className="text-foreground font-medium text-sm">{resource.name}</span>
                </div>
                <span className="text-foreground font-bold text-sm">
                  {resource.value}{resource.unit}
                </span>
              </div>
              <div className="space-y-2">
                <div className="w-full bg-muted/20 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full bg-gradient-to-r ${resource.color} transition-all duration-500`}
                    style={{ width: `${(resource.value / resource.max) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0{resource.unit}</span>
                  <span>{resource.max}{resource.unit}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
