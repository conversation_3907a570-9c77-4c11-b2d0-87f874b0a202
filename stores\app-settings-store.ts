"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"

type AppSettings = {
  panelUrl: string
  setPanelUrl: (url: string) => void
  logoDataUrl?: string | null
  setLogo: (dataUrl: string | null) => void
}

export const useAppSettings = create<AppSettings>()(
  persist(
    (set) => ({
      panelUrl: "https://panel.example.com",
      setPanelUrl: (url) => set({ panelUrl: url }),
      logoDataUrl: null,
      setLogo: (dataUrl) => set({ logoDataUrl: dataUrl }),
    }),
    { name: "app-settings" }
  )
)
