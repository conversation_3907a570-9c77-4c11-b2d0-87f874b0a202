"use client"

import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ServerIcon, Users, Cpu, HardDrive, Activity, ExternalLink } from 'lucide-react'
import type { Server } from "@/stores/server-store"
import { useAppSettings } from "@/stores/app-settings-store"

export default function ServerDetailsView({ server }: { server: Server }) {
  const { panelUrl } = useAppSettings()
  const ext = panelUrl || "https://panel.example.com"

  return (
    <div className="space-y-6">
      {/* Summary header */}
      <div className="glass-ultra rounded-2xl p-6 border border-border shadow-glass">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 rounded-2xl bg-brand flex items-center justify-center shadow-glass">
              <ServerIcon className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="brand-heading text-2xl md:text-3xl font-black">{server.name}</h1>
              <p className="text-muted-foreground text-sm mt-1">
                {server.type} • Status: <span className="capitalize">{server.status}</span> • Uptime: {server.uptime}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              className="premium-button"
              onClick={() => window.open(ext, "_blank")}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Manage in Pterodactyl
            </Button>
          </div>
        </div>

        {/* Quick stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-border/50">
          <Stat label="Players" value={server.players} icon={Users} />
          <Stat label="CPU" value={server.cpu} icon={Cpu} />
          <Stat label="Memory" value={server.memory} icon={HardDrive} />
          <Stat label="Network" value="1.2MB/s" icon={Activity} />
        </div>
      </div>

      {/* Info card */}
      <Card className="glass-ultra border border-border shadow-glass p-6">
        <p className="text-muted-foreground">
          This dashboard is for discovery, creation, and quick overview. Use your Pterodactyl panel for full server management (console, files, players, databases, etc.).
        </p>
      </Card>
    </div>
  )
}

function Stat({ label, value, icon: Icon }: { label: string; value: string; icon: any }) {
  return (
    <div className="text-center">
      <div className="flex items-center justify-center gap-2 mb-1">
        <Icon className="w-4 h-4 text-muted-foreground" />
        <span className="text-muted-foreground text-sm">{label}</span>
      </div>
      <p className="text-xl font-bold text-foreground">{value}</p>
    </div>
  )
}
