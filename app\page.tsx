"use client"

import PanelLayout from "@/components/panel-layout"
import DashboardView from "@/components/dashboard-view"
import { useServerStore } from "@/stores/server-store"
import { useRouter } from "next/navigation"

export default function PterodactylDashboard() {
  const servers = useServerStore((s) => s.servers)
  const router = useRouter()

  return (
    <PanelLayout title="Dashboard" subtitle="Your overview and quick actions">
      <DashboardView
        onServerOpen={(id) => router.push(`/servers/${id}`)}
        serverCount={servers.length}
      />
    </PanelLayout>
  )
}
