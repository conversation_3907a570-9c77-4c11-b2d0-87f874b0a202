"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"

type Code = { code: string; amount: number; used?: boolean; usedBy?: string }

type CodeStore = {
  codes: Code[]
  add: (c: Code) => void
  remove: (code: string) => void
  get: (code: string) => Code | undefined
  markUsed: (code: string, user: string) => void
}

export const useCodeStore = create<CodeStore>()(
  persist(
    (set, get) => ({
      codes: [{ code: "CYTHRO-200-ABCD", amount: 200, used: false }],
      add: (c) => set(s => ({ codes: [c, ...s.codes] })),
      remove: (code) => set(s => ({ codes: s.codes.filter(x => x.code !== code) })),
      get: (code) => get().codes.find(x => x.code === code),
      markUsed: (code, user) => set(s => ({ codes: s.codes.map(x => x.code === code ? { ...x, used: true, usedBy: user } : x) })),
    }),
    { name: "codes" }
  )
)
