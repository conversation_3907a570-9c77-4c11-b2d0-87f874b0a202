"use client"

import { useState, useMemo } from "react"
import PanelLayout from "@/components/panel-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Coins,
  TrendingUp,
  TrendingDown,
  Plus,
  Minus,
  ArrowUpRight,
  ArrowDownLeft,
  Gift,
  ShoppingCart,
  Users,
  Calendar,
  Search,
  Filter,
  Download,
  Eye,
  Zap,
  CreditCard,
  Wallet,
} from "lucide-react"
import { useWalletStore } from "@/stores/wallet-store"
import Link from "next/link"

export default function WalletPage() {
  const { balance, transactions } = useWalletStore()
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [timeRange, setTimeRange] = useState("7d")

  // Memoized calculations for better performance
  const walletStats = useMemo(() => {
    const weeklyTransactions = transactions.filter((t) => {
      const transactionDate = new Date(t.date || Date.now())
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      return transactionDate >= weekAgo
    })

    const weeklyIncome = weeklyTransactions.filter((t) => t.amount > 0).reduce((sum, t) => sum + t.amount, 0)

    const weeklyExpenses = weeklyTransactions
      .filter((t) => t.amount < 0)
      .reduce((sum, t) => sum + Math.abs(t.amount), 0)

    const monthlyTransactions = transactions.filter((t) => {
      const transactionDate = new Date(t.date || Date.now())
      const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      return transactionDate >= monthAgo
    })

    return {
      weeklyIncome,
      weeklyExpenses,
      weeklyNet: weeklyIncome - weeklyExpenses,
      monthlyTransactions: monthlyTransactions.length,
      totalTransactions: transactions.length,
    }
  }, [transactions])

  // Filter transactions based on search and filters
  const filteredTransactions = useMemo(() => {
    return transactions.filter((transaction) => {
      const matchesSearch =
        !searchQuery || (transaction.note && transaction.note.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesType =
        filterType === "all" ||
        (filterType === "income" && transaction.amount > 0) ||
        (filterType === "expense" && transaction.amount < 0)

      return matchesSearch && matchesType
    })
  }, [transactions, searchQuery, filterType])

  // Safe function to get transaction icon
  const getTransactionIcon = (amount: number, note?: string) => {
    if (amount > 0) {
      if (note?.toLowerCase().includes("referral")) return <Users className="w-4 h-4 text-emerald-400" />
      if (note?.toLowerCase().includes("reward") || note?.toLowerCase().includes("bonus"))
        return <Gift className="w-4 h-4 text-emerald-400" />
      if (note?.toLowerCase().includes("deposit")) return <Plus className="w-4 h-4 text-emerald-400" />
      return <ArrowUpRight className="w-4 h-4 text-emerald-400" />
    } else {
      if (note?.toLowerCase().includes("server")) return <ShoppingCart className="w-4 h-4 text-red-400" />
      if (note?.toLowerCase().includes("transfer")) return <ArrowDownLeft className="w-4 h-4 text-red-400" />
      if (note?.toLowerCase().includes("withdrawal")) return <Minus className="w-4 h-4 text-red-400" />
      return <ArrowDownLeft className="w-4 h-4 text-red-400" />
    }
  }

  // Safe function to get transaction color
  const getTransactionColor = (amount: number, note?: string) => {
    if (amount > 0) {
      return "text-emerald-400"
    } else {
      return "text-red-400"
    }
  }

  return (
    <PanelLayout title="Wallet" subtitle="Manage your coins and transaction history">
      <div className="space-y-8">
        {/* Wallet Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="glass-ultra border border-border rounded-2xl shadow-glass col-span-1 md:col-span-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-muted-foreground text-sm font-medium">Current Balance</p>
                  <div className="flex items-center gap-3 mt-2">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center shadow-lg">
                      <Coins className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="text-3xl font-black text-foreground">{balance.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">BlockCoins</p>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <Link href="/earn">
                    <Button size="sm" className="premium-button">
                      <Plus className="w-4 h-4 mr-2" />
                      Earn More
                    </Button>
                  </Link>
                  <Link href="/transfer">
                    <Button size="sm" variant="outline" className="secondary-button bg-transparent">
                      <ArrowUpRight className="w-4 h-4 mr-2" />
                      Transfer
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Weekly Stats */}
              <div className="grid grid-cols-3 gap-4 pt-4 border-t border-border/50">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <TrendingUp className="w-4 h-4 text-emerald-400" />
                    <span className="text-xs text-muted-foreground">Income</span>
                  </div>
                  <p className="text-lg font-bold text-emerald-400">+{walletStats.weeklyIncome}</p>
                  <p className="text-xs text-muted-foreground">This week</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <TrendingDown className="w-4 h-4 text-red-400" />
                    <span className="text-xs text-muted-foreground">Expenses</span>
                  </div>
                  <p className="text-lg font-bold text-red-400">-{walletStats.weeklyExpenses}</p>
                  <p className="text-xs text-muted-foreground">This week</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Zap className="w-4 h-4 text-blue-400" />
                    <span className="text-xs text-muted-foreground">Net</span>
                  </div>
                  <p
                    className={`text-lg font-bold ${walletStats.weeklyNet >= 0 ? "text-emerald-400" : "text-red-400"}`}
                  >
                    {walletStats.weeklyNet >= 0 ? "+" : ""}
                    {walletStats.weeklyNet}
                  </p>
                  <p className="text-xs text-muted-foreground">This week</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{walletStats.monthlyTransactions}</p>
                  <p className="text-sm text-muted-foreground">Monthly Transactions</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-emerald-400" />
                <span className="text-sm text-emerald-400 font-medium">+12%</span>
                <span className="text-sm text-muted-foreground">vs last month</span>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-green-500 flex items-center justify-center">
                  <Wallet className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{walletStats.totalTransactions}</p>
                  <p className="text-sm text-muted-foreground">Total Transactions</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-blue-400 font-medium">All time</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-foreground flex items-center gap-2">
              <Zap className="w-5 h-5 text-brand" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/earn">
                <Button className="w-full premium-button h-12">
                  <Plus className="w-4 h-4 mr-2" />
                  Earn Coins
                </Button>
              </Link>
              <Link href="/transfer">
                <Button variant="outline" className="w-full secondary-button bg-transparent h-12">
                  <ArrowUpRight className="w-4 h-4 mr-2" />
                  Transfer
                </Button>
              </Link>
              <Link href="/redeem">
                <Button variant="outline" className="w-full secondary-button bg-transparent h-12">
                  <Gift className="w-4 h-4 mr-2" />
                  Redeem Code
                </Button>
              </Link>
              <Button variant="outline" className="w-full secondary-button bg-transparent h-12">
                <Download className="w-4 h-4 mr-2" />
                Export History
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Transaction History */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <div className="flex flex-col sm:flex-row gap-4 justify-between">
              <CardTitle className="text-xl font-bold text-foreground flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-brand" />
                Transaction History
              </CardTitle>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search transactions..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 glass-input w-full sm:w-64"
                  />
                </div>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="glass-input w-full sm:w-32">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="glass-ultra border-border">
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="income">Income</SelectItem>
                    <SelectItem value="expense">Expenses</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredTransactions.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-gray-500/20 to-gray-600/20 flex items-center justify-center">
                    <Coins className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    {searchQuery || filterType !== "all" ? "No transactions found" : "No transactions yet"}
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    {searchQuery || filterType !== "all"
                      ? "Try adjusting your search or filter criteria."
                      : "Start earning coins to see your transaction history!"}
                  </p>
                  {!searchQuery && filterType === "all" && (
                    <Link href="/earn">
                      <Button className="premium-button">
                        <Plus className="w-4 h-4 mr-2" />
                        Start Earning
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                filteredTransactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50 hover:bg-accent/5 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-gray-500/20 to-gray-600/20 flex items-center justify-center">
                        {getTransactionIcon(transaction.amount, transaction.note)}
                      </div>
                      <div>
                        <p className="font-semibold text-foreground">{transaction.note || "Transaction"}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(transaction.date || Date.now()).toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                            year: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold text-lg ${getTransactionColor(transaction.amount, transaction.note)}`}>
                        {transaction.amount > 0 ? "+" : ""}
                        {transaction.amount.toLocaleString()}
                      </p>
                      <Badge
                        className={
                          transaction.amount > 0
                            ? "bg-emerald-500/20 text-emerald-400 border-emerald-500/30"
                            : "bg-red-500/20 text-red-400 border-red-500/30"
                        }
                      >
                        {transaction.amount > 0 ? "Income" : "Expense"}
                      </Badge>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </PanelLayout>
  )
}
