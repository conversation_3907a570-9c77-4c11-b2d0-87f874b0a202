"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Gamepad2, Code, Database, Palette, Shield, Rocket } from 'lucide-react'

export default function ServicesSection() {
  return (
    <section className="py-20 md:py-32 relative z-10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-6xl lg:text-8xl font-black mb-8 text-white">
            What We're
            <span className="block bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent enhanced-gradient">
              Really Good At
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto font-light leading-relaxed">
            We've spent years perfecting our craft. Here's what we love doing most and what our clients keep coming back for.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              icon: Gamepad2,
              title: "Game Development",
              description: "We create games that people actually want to play. From addictive mobile games to nostalgic recreations, we focus on fun first.",
              gradient: "from-purple-500 via-pink-500 to-red-500",
            },
            {
              icon: Code,
              title: "Web Development",
              description: "Beautiful websites that work perfectly on every device. We build them fast, make them secure, and ensure they convert visitors into customers.",
              gradient: "from-[#780206] via-[#780206] to-pink-500",
            },
            {
              icon: Database,
              title: "API Development",
              description: "Rock-solid backends that can handle anything you throw at them. We build APIs that scale with your business and never let you down.",
              gradient: "from-emerald-400 to-cyan-500",
            },
            {
              icon: Palette,
              title: "UI/UX Design",
              description: "Designs that make users go 'wow!' We create interfaces that are not just pretty, but actually make sense and feel great to use.",
              gradient: "from-orange-400 via-pink-500 to-purple-500",
            },
            {
              icon: Shield,
              title: "Security Solutions",
              description: "Sleep better at night knowing your digital assets are protected. We implement security that's tough on hackers but invisible to users.",
              gradient: "from-blue-500 via-indigo-500 to-purple-500",
            },
            {
              icon: Rocket,
              title: "Performance Optimization",
              description: "Nobody likes waiting. We make your apps lightning-fast and your users happy. Every millisecond counts, and we count them all.",
              gradient: "from-cyan-400 via-blue-500 to-[#061161]",
            },
          ].map((service, index) => (
            <Card
              key={index}
              className="group glass-ultra border border-white/20 hover:bg-white/[0.12] transition-all duration-500 hover:scale-105 rounded-3xl overflow-hidden shadow-glass"
            >
              <CardContent className="p-8 relative">
                <div className="relative z-10">
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${service.gradient} flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 enhanced-gradient-bg shadow-lg`}
                  >
                    <service.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-6">{service.title}</h3>
                  <p className="text-gray-300 leading-relaxed text-lg">{service.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
