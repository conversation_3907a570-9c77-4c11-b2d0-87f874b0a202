"use client"

import { useState } from "react"
import { useUserStore, type Role } from "@/stores/user-store"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Trash2, UserPlus } from 'lucide-react'

export default function AdminUsers() {
  const { users, create, remove, update } = useUserStore()
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [role, setRole] = useState<Role>("User")

  const onAdd = () => {
    if (!name.trim() || !email.trim()) return
    create({ name, email, role, active: true })
    setName("")
    setEmail("")
    setRole("User")
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground text-xl">Users & Roles</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Create user */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <div className="space-y-2">
            <Label className="text-foreground">Name</Label>
            <Input value={name} onChange={(e) => setName(e.target.value)} placeholder="Jane Doe" className="glass-ultra border-border" />
          </div>
          <div className="space-y-2">
            <Label className="text-foreground">Email</Label>
            <Input value={email} onChange={(e) => setEmail(e.target.value)} placeholder="<EMAIL>" className="glass-ultra border-border" />
          </div>
          <div className="space-y-2">
            <Label className="text-foreground">Role</Label>
            <Select value={role} onValueChange={(v) => setRole(v as Role)}>
              <SelectTrigger className="glass-ultra border-border">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                {["Admin", "Moderator", "User"].map((r) => (
                  <SelectItem key={r} value={r}>{r}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end">
            <Button className="premium-button w-full" onClick={onAdd}>
              <UserPlus className="w-4 h-4 mr-2" />
              Add User
            </Button>
          </div>
        </div>

        {/* Users list */}
        <div className="space-y-3">
          {users.map((u) => (
            <div key={u.id} className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50">
              <div className="min-w-0">
                <p className="text-foreground font-medium truncate">{u.name} <span className="text-muted-foreground">{"<"}{u.email}{">"}</span></p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="bg-gradient-to-r from-[rgb(120,2,6)]/15 to-[rgb(6,17,97)]/15 text-foreground border border-border">
                    {u.role}
                  </Badge>
                  <span className={`text-xs ${u.active ? "text-emerald-400" : "text-muted-foreground"}`}>
                    {u.active ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Active</span>
                  <Switch checked={u.active} onCheckedChange={(v) => update(u.id, { active: v })} />
                </div>
                <Select value={u.role} onValueChange={(v) => update(u.id, { role: v as Role })}>
                  <SelectTrigger className="glass-ultra border-border w-[130px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {["Admin", "Moderator", "User"].map((r) => (
                      <SelectItem key={r} value={r}>{r}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="ghost" className="glass-button p-2 text-red-400 hover:text-red-300" onClick={() => remove(u.id)}>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
