"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { nanoid } from "nanoid"
import { useNotificationStore } from "./notification-store"

type Tx = { id: string; amount: number; note: string; date: string }

type WalletStore = {
  balance: number
  transactions: Tx[]
  addCoins: (amount: number, note: string) => void
  spendCoins: (amount: number, note: string) => boolean
  transfer: (email: string, amount: number) => boolean
}

export const useWalletStore = create<WalletStore>()(
  persist(
    (set, get) => ({
      balance: 200,
      transactions: [],
      addCoins: (amount, note) => {
        set(s => ({ balance: s.balance + amount, transactions: [{ id: nanoid(), amount, note, date: new Date().toISOString() }, ...s.transactions] }))
        useNotificationStore.getState().push({ title: "Coins received", message: `${amount} coins: ${note}` })
      },
      spendCoins: (amount, note) => {
        const { balance } = get()
        if (balance < amount) return false
        set(s => ({ balance: s.balance - amount, transactions: [{ id: nanoid(), amount: -amount, note, date: new Date().toISOString() }, ...s.transactions] }))
        useNotificationStore.getState().push({ title: "Coins spent", message: `${amount} coins: ${note}` })
        return true
      },
      transfer: (email, amount) => {
        const ok = get().spendCoins(amount, `Transfer to ${email}`)
        if (ok) useNotificationStore.getState().push({ title: "Transfer sent", message: `${amount} coins to ${email}` })
        return ok
      },
    }),
    { name: "wallet" }
  )
)
