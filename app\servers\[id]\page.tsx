"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import PanelLayout from "@/components/panel-layout"
import ServerDetailsView from "@/components/server-details-view"
import { useServerStore } from "@/stores/server-store"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from 'lucide-react'

export default function ServerDetailsPage() {
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const server = useServerStore((s) => s.getById(params.id))

  if (!server) {
    return (
      <PanelLayout title="Server not found" subtitle="The server may have been removed">
        <div className="glass-ultra rounded-2xl p-6 border border-border shadow-glass">
          <p className="text-muted-foreground mb-4">{'We could not find this server.'}</p>
          <Button variant="outline" className="secondary-button" onClick={() => router.push("/servers")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Servers
          </Button>
        </div>
      </PanelLayout>
    )
  }

  return (
    <PanelLayout title={server.name} subtitle={`${server.type} • ${server.status.toUpperCase()}`}>
      <ServerDetailsView server={server} />
    </PanelLayout>
  )
}
