"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Minus } from "lucide-react"

type StatCardProps = {
  title: string
  value: string
  change?: number
  changeLabel?: string
  color: string
  icon?: React.ReactNode
}

export default function EnhancedDashboardStats({ stats }: { stats: StatCardProps[] }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  )
}

function StatCard({ title, value, change, changeLabel, color, icon }: StatCardProps) {
  const getTrendIcon = () => {
    if (!change) return <Minus className="w-3 h-3" />
    if (change > 0) return <TrendingUp className="w-3 h-3" />
    return <TrendingDown className="w-3 h-3" />
  }

  const getTrendColor = () => {
    if (!change) return "text-muted-foreground"
    if (change > 0) return "text-emerald-400"
    return "text-red-400"
  }

  return (
    <Card className="glass-ultra border border-border shadow-glass rounded-2xl overflow-hidden group hover:shadow-glass-lg transition-all duration-300 animate-slide-up">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <h3 className="text-muted-foreground text-sm font-medium">{title}</h3>
          </div>
          {icon && (
            <div
              className={`w-10 h-10 rounded-xl bg-gradient-to-br ${color} flex items-center justify-center shadow-lg`}
            >
              {icon}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <p className="text-3xl font-black text-foreground">{value}</p>

          {change !== undefined && (
            <div className="flex items-center gap-1">
              <div className={`flex items-center gap-1 ${getTrendColor()}`}>
                {getTrendIcon()}
                <span className="text-xs font-medium">{Math.abs(change)}%</span>
              </div>
              {changeLabel && <span className="text-xs text-muted-foreground">{changeLabel}</span>}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
