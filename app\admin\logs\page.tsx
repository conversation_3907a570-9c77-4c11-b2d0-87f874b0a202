"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, Search, Filter, RefreshCw } from "lucide-react"

export default function AdminLogsPage() {
  const logs = [
    { time: "2024-01-09 14:32:15", level: "INFO", service: "Auth", message: "User login successful: <EMAIL>" },
    {
      time: "2024-01-09 14:31:45",
      level: "WARN",
      service: "Server",
      message: "High CPU usage detected on minecraft_survival",
    },
    {
      time: "2024-01-09 14:30:12",
      level: "ERROR",
      service: "Database",
      message: "Connection timeout to mysql_primary",
    },
    { time: "2024-01-09 14:29:33", level: "INFO", service: "Panel", message: "Server created: rust_pvp_new" },
    {
      time: "2024-01-09 14:28:55",
      level: "DEBUG",
      service: "API",
      message: "Rate limit check passed for IP *************",
    },
  ]

  const getLevelColor = (level: string) => {
    switch (level) {
      case "ERROR":
        return "text-red-400 bg-red-400/10"
      case "WARN":
        return "text-yellow-400 bg-yellow-400/10"
      case "INFO":
        return "text-blue-400 bg-blue-400/10"
      case "DEBUG":
        return "text-gray-400 bg-gray-400/10"
      default:
        return "text-muted-foreground bg-muted/10"
    }
  }

  return (
    <AdminLayout title="System Logs" subtitle="Monitor system events and troubleshoot issues">
      <div className="space-y-6">
        {/* Filters */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-foreground">Log Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input placeholder="Search logs..." className="pl-10 glass-ultra border-border" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="glass-ultra border-border">
                  <SelectValue placeholder="Log Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="warn">Warning</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="debug">Debug</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all">
                <SelectTrigger className="glass-ultra border-border">
                  <SelectValue placeholder="Service" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Services</SelectItem>
                  <SelectItem value="auth">Auth</SelectItem>
                  <SelectItem value="server">Server</SelectItem>
                  <SelectItem value="database">Database</SelectItem>
                  <SelectItem value="panel">Panel</SelectItem>
                  <SelectItem value="api">API</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex gap-2">
                <Button variant="outline" className="secondary-button bg-transparent">
                  <Filter className="w-4 h-4 mr-2" />
                  Apply
                </Button>
                <Button variant="outline" className="secondary-button bg-transparent">
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Logs Display */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-foreground">Recent Logs</CardTitle>
              <Button variant="outline" className="secondary-button bg-transparent">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {logs.map((log, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 p-4 glass-ultra rounded-xl border border-border/50 hover:bg-accent/5 transition-colors"
                >
                  <div className="text-muted-foreground text-sm font-mono w-36 flex-shrink-0">{log.time}</div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(log.level)} flex-shrink-0`}>
                    {log.level}
                  </div>
                  <div className="text-muted-foreground text-sm w-20 flex-shrink-0">{log.service}</div>
                  <div className="text-foreground text-sm flex-1 min-w-0">{log.message}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
