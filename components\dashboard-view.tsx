"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Server, Users, Activity, Zap } from 'lucide-react'
import EnhancedDashboardStats from "@/components/enhanced-dashboard-stats"
import { ActivityFeed } from "@/components/activity-feed"
import ServerGrid from "@/components/server-grid"
import { useServerStore } from "@/stores/server-store"

interface DashboardViewProps {
  onServerOpen: (id: string) => void
  serverCount: number
}

export default function DashboardView({ onServerOpen, serverCount }: DashboardViewProps) {
  const { servers } = useServerStore()

  const stats = [
    {
      title: "Total Servers",
      value: serverCount,
      change: "+2",
      trend: "up" as const,
      icon: Server,
      color: "from-blue-500 to-cyan-500",
    },
    {
      title: "Active Players",
      value: servers.reduce((sum, server) => sum + server.players.current, 0),
      change: "+12",
      trend: "up" as const,
      icon: Users,
      color: "from-emerald-500 to-green-500",
    },
    {
      title: "CPU Usage",
      value: `${Math.round(servers.reduce((sum, server) => sum + server.resources.cpu, 0) / servers.length || 0)}%`,
      change: "-5%",
      trend: "down" as const,
      icon: Activity,
      color: "from-orange-500 to-red-500",
    },
    {
      title: "Memory Usage",
      value: `${Math.round(servers.reduce((sum, server) => sum + server.resources.memory, 0) / servers.length || 0)}%`,
      change: "0%",
      trend: "neutral" as const,
      icon: Server,
      color: "from-purple-500 to-pink-500",
    },
  ]

  const getTrendIcon = (trend: "up" | "down" | "neutral") => {
    switch (trend) {
      case "up":
        return <Zap className="w-4 h-4 text-emerald-400" />
      case "down":
        return <Zap className="w-4 h-4 text-red-400" />
      default:
        return <Zap className="w-4 h-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend: "up" | "down" | "neutral") => {
    switch (trend) {
      case "up":
        return "text-emerald-400"
      case "down":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
        <div>
          <h1 className="text-3xl md:text-4xl font-black text-foreground brand-heading">Welcome back! 👋</h1>
          <p className="text-muted-foreground mt-2 text-lg">Here's what's happening with your servers today.</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button className="premium-button w-full sm:w-auto" onClick={() => onServerOpen("new")}>
            <Plus className="w-4 h-4 mr-2" />
            Create Server
          </Button>
          <Button variant="outline" className="secondary-button bg-transparent w-full sm:w-auto" onClick={() => onServerOpen("all")}>
            <Server className="w-4 h-4 mr-2" />
            View All Servers
          </Button>
        </div>
      </div>

      {/* Enhanced Stats Section */}
      <EnhancedDashboardStats stats={stats} />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Server Grid - Takes up 2 columns on large screens */}
        <div className="lg:col-span-2">
          <ServerGrid />
        </div>

        {/* Activity Feed - Takes up 1 column on large screens */}
        <div className="lg:col-span-1">
          <ActivityFeed />
        </div>
      </div>
    </div>
  )
}
