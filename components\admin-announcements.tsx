"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useAnnouncementStore } from "@/stores/announcement-store"
import { Trash2 } from 'lucide-react'

export default function AdminAnnouncements() {
  const { announcements, add, remove } = useAnnouncementStore()
  const [title, setTitle] = useState("")
  const [body, setBody] = useState("")

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground">Create Announcement</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <Input placeholder="Title" className="glass-ultra border-border md:col-span-1" value={title} onChange={(e) => setTitle(e.target.value)} />
          <Textarea placeholder="Message..." className="glass-ultra border-border md:col-span-2" rows={3} value={body} onChange={(e) => setBody(e.target.value)} />
        </div>
        <div className="flex justify-end">
          <Button className="premium-button" onClick={() => { if (!title.trim()) return; add({ title, body }); setTitle(""); setBody("") }}>
            Publish
          </Button>
        </div>

        <div className="space-y-2">
          {announcements.length === 0 ? (
            <p className="text-sm text-muted-foreground">{'No announcements yet.'}</p>
          ) : announcements.map(a => (
            <div key={a.id} className="flex items-start justify-between p-3 glass-ultra rounded-xl border border-border/50">
              <div>
                <p className="text-foreground font-medium">{a.title}</p>
                <p className="text-sm text-muted-foreground">{a.body}</p>
                <p className="text-xs text-muted-foreground mt-1">{new Date(a.createdAt).toLocaleString()}</p>
              </div>
              <Button variant="ghost" className="glass-button p-2 text-red-400 hover:text-red-300" onClick={() => remove(a.id)}>
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
