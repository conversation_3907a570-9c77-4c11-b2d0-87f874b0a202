"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Play,
  Square,
  RotateCcw,
  MoreHorizontal,
  Users,
  Cpu,
  HardDrive,
  Settings,
  FileText,
  Trash2,
  Copy,
} from "lucide-react"

type ServerCardProps = {
  id: string
  name: string
  type: string
  status: "online" | "offline" | "starting" | "stopping"
  players: string
  cpu: number
  memory: number
  onSelect: () => void
}

export default function EnhancedServerCard({
  id,
  name,
  type,
  status,
  players,
  cpu,
  memory,
  onSelect,
}: ServerCardProps) {
  const getStatusColor = () => {
    switch (status) {
      case "online":
        return "bg-emerald-500"
      case "offline":
        return "bg-red-500"
      case "starting":
        return "bg-yellow-500"
      case "stopping":
        return "bg-orange-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusBadge = () => {
    switch (status) {
      case "online":
        return <Badge className="bg-emerald-500/20 text-emerald-500 border-emerald-500/30">Online</Badge>
      case "offline":
        return <Badge variant="destructive">Offline</Badge>
      case "starting":
        return <Badge className="bg-yellow-500/20 text-yellow-500 border-yellow-500/30">Starting</Badge>
      case "stopping":
        return <Badge className="bg-orange-500/20 text-orange-500 border-orange-500/30">Stopping</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  return (
    <Card className="glass-ultra border border-border shadow-glass rounded-2xl overflow-hidden hover:shadow-glass-lg transition-all duration-300 group">
      <CardContent className="p-0">
        {/* Fixed height header to prevent layout issues */}
        <div className="h-20 p-4 border-b border-border/50 flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className={`w-3 h-3 rounded-full ${getStatusColor()} animate-pulse flex-shrink-0`} />
            <div className="min-w-0 flex-1">
              {/* Proper text truncation */}
              <h3 className="font-bold text-foreground truncate text-sm sm:text-base" title={name}>
                {name}
              </h3>
              <p className="text-xs text-muted-foreground truncate">{type}</p>
            </div>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            {getStatusBadge()}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="glass-button p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={onSelect}>
                  <Settings className="w-4 h-4 mr-2" />
                  Manage Server
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText className="w-4 h-4 mr-2" />
                  View Logs
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy IP
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-500">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Server
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Server stats */}
        <div className="p-4 space-y-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="flex items-center justify-center gap-1 mb-1">
                <Users className="w-3 h-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">Players</span>
              </div>
              <p className="text-sm font-bold">{players}</p>
            </div>
            <div>
              <div className="flex items-center justify-center gap-1 mb-1">
                <Cpu className="w-3 h-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">CPU</span>
              </div>
              <p className="text-sm font-bold">{cpu}%</p>
            </div>
            <div>
              <div className="flex items-center justify-center gap-1 mb-1">
                <HardDrive className="w-3 h-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">RAM</span>
              </div>
              <p className="text-sm font-bold">{memory}%</p>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2">
            {status === "online" ? (
              <Button size="sm" variant="destructive" className="flex-1">
                <Square className="w-3 h-3 mr-1" />
                Stop
              </Button>
            ) : (
              <Button
                size="sm"
                className="flex-1 bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] hover:from-[rgb(20,136,204)]/90 hover:to-[rgb(43,50,178)]/90 text-white"
              >
                <Play className="w-3 h-3 mr-1" />
                Start
              </Button>
            )}
            <Button size="sm" variant="outline" className="glass-button bg-transparent">
              <RotateCcw className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
