"use client"

import { useState } from "react"
import PanelLayout from "@/components/panel-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Gift,
  Ticket,
  Coins,
  Star,
  Crown,
  Gem,
  Sparkles,
  CheckCircle,
  Clock,
  X,
  MessageSquare,
  Share2,
  Play,
  Camera,
  Trophy,
  Zap,
  Target,
} from "lucide-react"
import { useWalletStore } from "@/stores/wallet-store"
import { useToast } from "@/hooks/use-toast"

const sampleCodes = [
  {
    id: 1,
    code: "WELCOME100",
    reward: 100,
    rarity: "common",
    description: "Welcome bonus for new users",
    icon: Gift,
    color: "from-blue-500 to-cyan-600",
    bgColor: "bg-blue-500/10",
    textColor: "text-blue-400",
    borderColor: "border-blue-500/20",
  },
  {
    id: 2,
    code: "DISCORD50",
    reward: 50,
    rarity: "common",
    description: "Join our Discord server",
    icon: MessageSquare,
    color: "from-indigo-500 to-purple-600",
    bgColor: "bg-indigo-500/10",
    textColor: "text-indigo-400",
    borderColor: "border-indigo-500/20",
  },
  {
    id: 3,
    code: "RARE250",
    reward: 250,
    rarity: "rare",
    description: "Special event reward",
    icon: Star,
    color: "from-purple-500 to-pink-600",
    bgColor: "bg-purple-500/10",
    textColor: "text-purple-400",
    borderColor: "border-purple-500/20",
  },
  {
    id: 4,
    code: "EPIC500",
    reward: 500,
    rarity: "epic",
    description: "Epic milestone achievement",
    icon: Crown,
    color: "from-amber-500 to-orange-600",
    bgColor: "bg-amber-500/10",
    textColor: "text-amber-400",
    borderColor: "border-amber-500/20",
  },
  {
    id: 5,
    code: "LEGENDARY1000",
    reward: 1000,
    rarity: "legendary",
    description: "Legendary exclusive code",
    icon: Gem,
    color: "from-red-500 to-pink-600",
    bgColor: "bg-red-500/10",
    textColor: "text-red-400",
    borderColor: "border-red-500/20",
  },
]

const howToGetCodes = [
  {
    platform: "Discord",
    description: "Join our Discord for exclusive codes",
    icon: MessageSquare,
    color: "from-indigo-500 to-purple-600",
    url: "https://discord.gg/example",
  },
  {
    platform: "Twitter",
    description: "Follow for giveaway codes",
    icon: Share2,
    color: "from-blue-400 to-blue-600",
    url: "https://twitter.com/pterodash",
  },
  {
    platform: "YouTube",
    description: "Subscribe for video codes",
    icon: Play,
    color: "from-red-500 to-red-600",
    url: "https://youtube.com/@pterodash",
  },
  {
    platform: "Instagram",
    description: "Follow for story codes",
    icon: Camera,
    color: "from-pink-500 to-purple-600",
    url: "https://instagram.com/pterodash",
  },
]

const recentRedemptions = [
  { id: 1, code: "WELCOME100", amount: 100, date: "2024-01-15", status: "success" },
  { id: 2, code: "DISCORD50", amount: 50, date: "2024-01-14", status: "success" },
  { id: 3, code: "INVALID123", amount: 0, date: "2024-01-13", status: "failed" },
]

export default function RedeemPage() {
  const [code, setCode] = useState("")
  const { addCoins } = useWalletStore()
  const { toast } = useToast()

  const handleRedeem = () => {
    if (!code.trim()) {
      toast({
        title: "Missing Code",
        description: "Please enter a redemption code.",
        variant: "destructive",
      })
      return
    }

    // Find matching sample code
    const matchingCode = sampleCodes.find((sample) => sample.code.toLowerCase() === code.toLowerCase())

    if (matchingCode) {
      addCoins(matchingCode.reward, `Redeemed code: ${matchingCode.code}`)
      toast({
        title: "Code Redeemed! 🎉",
        description: `Successfully redeemed ${matchingCode.reward} coins with code ${matchingCode.code}.`,
      })
      setCode("")
    } else {
      toast({
        title: "Invalid Code",
        description: "The code you entered is invalid or has already been used.",
        variant: "destructive",
      })
    }
  }

  const getRarityBadge = (rarity: string) => {
    switch (rarity) {
      case "common":
        return <Badge className="bg-blue-400/10 text-blue-400 border-blue-400/20">Common</Badge>
      case "rare":
        return <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">Rare</Badge>
      case "epic":
        return <Badge className="bg-amber-400/10 text-amber-400 border-amber-400/20">Epic</Badge>
      case "legendary":
        return <Badge className="bg-red-400/10 text-red-400 border-red-400/20">Legendary</Badge>
      default:
        return <Badge>Unknown</Badge>
    }
  }

  return (
    <PanelLayout title="Redeem Codes" subtitle="Enter codes to get free coins and exclusive rewards">
      <div className="space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)]/10 via-[rgb(32,141,209)]/8 to-[rgb(43,50,178)]/10 border border-[rgb(20,136,204)]/20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
          <div className="relative p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 border border-[rgb(20,136,204)]/30">
                  <Ticket className="w-4 h-4 text-[rgb(20,136,204)]" />
                  <span className="text-sm font-medium text-foreground">Free Rewards</span>
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-black brand-heading mb-4">Redeem Codes</h1>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Enter special codes to unlock free coins, exclusive rewards, and premium features. Follow our social
                    media channels to get the latest codes and giveaways.
                  </p>
                </div>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Zap className="w-5 h-5 text-blue-400" />
                    <span className="text-sm font-medium">Instant Rewards</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Trophy className="w-5 h-5 text-amber-400" />
                    <span className="text-sm font-medium">Exclusive Codes</span>
                  </div>
                  <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                    <Sparkles className="w-5 h-5 text-purple-400" />
                    <span className="text-sm font-medium">Special Events</span>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-32 h-32 rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] shadow-2xl shadow-[rgb(20,136,204)]/25 mb-6">
                  <Gift className="w-16 h-16 text-white" />
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Ready to Redeem</p>
                  <p className="text-4xl font-black text-foreground">Free Coins</p>
                  <p className="text-lg text-muted-foreground">Enter your code below</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Redeem Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Redeem Card */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                    <Ticket className="w-5 h-5 text-white" />
                  </div>
                  Enter Redemption Code
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="code" className="text-base font-semibold">
                    Redemption Code
                  </Label>
                  <div className="flex gap-3">
                    <Input
                      id="code"
                      placeholder="Enter your code here..."
                      value={code}
                      onChange={(e) => setCode(e.target.value.toUpperCase())}
                      className="glass-input h-12 text-lg font-mono"
                    />
                    <Button onClick={handleRedeem} className="premium-button h-12 px-8">
                      <Gift className="w-5 h-5 mr-2" />
                      Redeem
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">Codes are case-insensitive and can only be used once</p>
                </div>
              </CardContent>
            </Card>

            {/* Sample Codes */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  Sample Codes
                  <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">Try These!</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {sampleCodes.map((sample) => {
                    const IconComponent = sample.icon
                    return (
                      <div
                        key={sample.id}
                        className={`group p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:scale-105 ${sample.bgColor} ${sample.borderColor} hover:shadow-lg`}
                        onClick={() => setCode(sample.code)}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div
                            className={`w-10 h-10 rounded-xl bg-gradient-to-br ${sample.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform`}
                          >
                            <IconComponent className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex items-center gap-2">
                            {getRarityBadge(sample.rarity)}
                            <div className="flex items-center gap-1">
                              <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                              <span className="font-bold text-foreground">{sample.reward}</span>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <p className="font-mono text-sm font-bold text-foreground">{sample.code}</p>
                          <p className="text-xs text-muted-foreground">{sample.description}</p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* How to Get Codes */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                    <Target className="w-4 h-4 text-white" />
                  </div>
                  How to Get Codes
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {howToGetCodes.map((platform) => {
                  const IconComponent = platform.icon
                  return (
                    <div
                      key={platform.platform}
                      className="flex items-center gap-3 p-3 glass-ultra rounded-lg border border-border/50 hover:bg-accent/5 transition-colors cursor-pointer"
                      onClick={() => window.open(platform.url, "_blank")}
                    >
                      <div
                        className={`w-8 h-8 rounded-lg bg-gradient-to-br ${platform.color} flex items-center justify-center`}
                      >
                        <IconComponent className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-foreground">{platform.platform}</p>
                        <p className="text-xs text-muted-foreground">{platform.description}</p>
                      </div>
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* Recent Redemptions */}
            <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
                    <Clock className="w-4 h-4 text-white" />
                  </div>
                  Recent Redemptions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentRedemptions.map((redemption) => (
                    <div
                      key={redemption.id}
                      className="flex items-center justify-between p-3 glass-ultra rounded-lg border border-border/50"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            redemption.status === "success" ? "bg-emerald-500/20" : "bg-red-500/20"
                          }`}
                        >
                          {redemption.status === "success" ? (
                            <CheckCircle className="w-4 h-4 text-emerald-400" />
                          ) : (
                            <X className="w-4 h-4 text-red-400" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-foreground font-mono">{redemption.code}</p>
                          <p className="text-xs text-muted-foreground">{redemption.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        {redemption.amount > 0 ? (
                          <div className="flex items-center gap-1">
                            <Coins className="w-3 h-3 text-[rgb(20,136,204)]" />
                            <span className="text-sm font-bold text-foreground">+{redemption.amount}</span>
                          </div>
                        ) : (
                          <span className="text-sm text-red-400">Failed</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PanelLayout>
  )
}
