"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useCodeStore } from "@/stores/code-store"
import { nanoid } from "nanoid"
import { Trash2 } from "lucide-react"

export default function AdminCodes() {
  const { codes, add, remove } = useCodeStore()
  const create = (amountStr: string) => {
    const amount = Number(amountStr || "100")
    const code = `CYTHRO-${amount}-${nanoid(4).toUpperCase()}`
    add({ code, amount })
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground">Codes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <div className="space-y-2">
            <Label className="text-foreground">Amount</Label>
            <Input id="amount" defaultValue="100" className="glass-ultra border-border w-40" />
          </div>
          <Button
            className="premium-button self-end"
            onClick={() => {
              const el = document.getElementById("amount") as HTMLInputElement
              create(el?.value || "100")
            }}
          >
            Generate
          </Button>
        </div>

        <div className="space-y-2">
          {codes.length === 0 ? (
            <p className="text-sm text-muted-foreground">{"No codes yet."}</p>
          ) : (
            codes.map((c) => (
              <div
                key={c.code}
                className="flex items-center justify-between p-3 glass-ultra rounded-xl border border-border/50"
              >
                <div>
                  <p className="font-mono text-foreground">{c.code}</p>
                  <p className="text-xs text-muted-foreground">
                    {c.used ? `Used by ${c.usedBy ?? "unknown"}` : "Unused"}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-foreground font-medium bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] bg-clip-text text-transparent">
                    {c.amount}
                  </span>
                  <Button
                    variant="ghost"
                    className="glass-button p-2 text-red-400 hover:text-red-300"
                    onClick={() => remove(c.code)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
