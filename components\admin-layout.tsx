"use client"

import { useState } from "react"
import AdminSidebar from "@/components/admin-sidebar"
import PterodactylHeader from "@/components/pterodactyl-header"
import { ThemeProvider } from "@/components/theme-provider"
import { I18nProvider } from "@/components/i18n-provider"

export default function AdminLayout({ title = "Admin", subtitle = "Control Center", children }: { title?: string; subtitle?: string; children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <ThemeProvider>
      <I18nProvider>
        <div className="relative h-svh w-svw overflow-hidden bg-background">
          {/* Background */}
          <div aria-hidden className="pointer-events-none fixed inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background" />
            <div className="absolute inset-0 bg-grid-pattern opacity-30" />
          </div>

          {/* Mobile overlay */}
          {sidebarOpen && (
            <button aria-label="Close sidebar" className="fixed inset-0 z-40 bg-black/50 lg:hidden" onClick={() => setSidebarOpen(false)} />
          )}

          <div className="relative z-10 flex h-full">
            <AdminSidebar />
            <div className="flex min-w-0 flex-1 flex-col">
              <div className="sticky top-0 z-30">
                <PterodactylHeader setSidebarOpen={setSidebarOpen} title={title} subtitle={subtitle} />
              </div>
              <main className="flex-1 overflow-y-auto px-4 py-4 md:px-6 md:py-6">
                <div className="pt-[env(safe-area-inset-top)] pb-[env(safe-area-inset-bottom)]">
                  {children}
                </div>
              </main>
            </div>
          </div>
        </div>
      </I18nProvider>
    </ThemeProvider>
  )
}
