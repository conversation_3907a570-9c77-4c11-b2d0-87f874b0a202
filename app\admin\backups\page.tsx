"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

export default function AdminBackupsPage() {
  const backups = [
    { id: "b1", name: "mc-survival-2025-08-01.zip", size: "1.2 GB" },
    { id: "b2", name: "rust-pvp-2025-08-03.zip", size: "850 MB" },
  ]

  return (
    <AdminLayout title="Backups" subtitle="Upload or download server backups">
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader><CardTitle className="text-foreground">Backups</CardTitle></CardHeader>
        <CardContent className="space-y-2">
          {backups.map(b => (
            <div key={b.id} className="flex items-center justify-between p-3 glass-ultra rounded-xl border border-border/50">
              <span className="text-foreground">{b.name}</span>
              <span className="text-sm text-muted-foreground">{b.size}</span>
            </div>
          ))}
          <p className="text-xs text-muted-foreground">{'Stub UI. Connect to storage later.'}</p>
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
