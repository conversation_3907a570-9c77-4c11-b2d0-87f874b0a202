"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Save, ImageIcon, X } from 'lucide-react'
import { useAppSettings } from "@/stores/app-settings-store"
import { useToast } from "@/hooks/use-toast"
import { useRef, useState } from "react"

export default function SettingsGeneral() {
  const { panelUrl, setPanelUrl, logoDataUrl, setLogo } = useAppSettings()
  const { toast } = useToast()
  const fileRef = useRef<HTMLInputElement | null>(null)
  const [tempUrl, setTempUrl] = useState(panelUrl)

  function onPickLogo() {
    fileRef.current?.click()
  }

  function onFile(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0]
    if (!file) return
    const reader = new FileReader()
    reader.onload = () => {
      setLogo(String(reader.result))
      toast({ title: "Logo updated", description: "Your sidebar logo was saved." })
    }
    reader.readAsDataURL(file)
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground text-xl">General</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-foreground">Organization</Label>
            <Input placeholder="Cythro" className="glass-ultra border-border" defaultValue="Cythro" />
          </div>
          <div className="space-y-2">
            <Label className="text-foreground">Support Email</Label>
            <Input placeholder="<EMAIL>" className="glass-ultra border-border" defaultValue="<EMAIL>" />
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-foreground">Default Server Region</Label>
          <Input placeholder="e.g. eu-central" className="glass-ultra border-border" defaultValue="eu-central" />
        </div>

        <div className="space-y-2">
          <Label className="text-foreground">Pterodactyl Panel URL</Label>
          <Input
            value={tempUrl}
            onChange={(e) => setTempUrl(e.target.value)}
            placeholder="https://panel.example.com"
            className="glass-ultra border-border"
          />
          <p className="text-xs text-muted-foreground">
            This dashboard will open management links on your Pterodactyl panel.
          </p>
        </div>

        <div className="space-y-3">
          <Label className="text-foreground">Brand Logo</Label>
          <div className="flex items-center gap-3">
            {logoDataUrl ? (
              <img
                src={logoDataUrl || "/placeholder.svg"}
                alt="Brand logo"
                className="w-12 h-12 rounded-xl object-cover border border-border shadow-glass"
              />
            ) : (
              <div className="w-12 h-12 rounded-xl bg-brand border border-border shadow-glass" />
            )}
            <input
              ref={fileRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={onFile}
            />
            <Button variant="outline" className="secondary-button" onClick={onPickLogo}>
              <ImageIcon className="w-4 h-4 mr-2" />
              {logoDataUrl ? "Change Logo" : "Upload Logo"}
            </Button>
            {logoDataUrl && (
              <Button
                variant="outline"
                className="secondary-button"
                onClick={() => { setLogo(null); toast({ title: "Logo removed" }) }}
              >
                <X className="w-4 h-4 mr-2" />
                Remove
              </Button>
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            Recommended: square image, 128×128 or larger.
          </p>
        </div>

        <div className="flex justify-end">
          <Button className="premium-button" onClick={() => { setPanelUrl(tempUrl); toast({ title: "Saved", description: "Settings updated." }) }}>
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
