"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Server,
  Play,
  Square,
  RotateCcw,
  Settings,
  Trash2,
  MoreHorizontal,
  Users,
  Activity,
  HardDrive,
  Search,
  Plus,
  ExternalLink,
} from "lucide-react"
import Link from "next/link"
import { useServerStore } from "@/stores/server-store"
import { DeleteConfirmationDialog } from "@/components/delete-confirmation-dialog"

export default function ServerGrid() {
  const { servers, updateServerStatus, deleteServer } = useServerStore()
  const [searchQuery, setSearchQuery] = useState("")
  const [deleteDial<PERSON>, setDeleteDialog] = useState<{
    open: boolean
    server: any | null
  }>({ open: false, server: null })

  const filteredServers = servers.filter(
    (server) =>
      server.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.game.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30"
      case "offline":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "starting":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "stopping":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const handleServerAction = (serverId: string, action: string) => {
    switch (action) {
      case "start":
        updateServerStatus(serverId, "starting")
        setTimeout(() => updateServerStatus(serverId, "online"), 2000)
        break
      case "stop":
        updateServerStatus(serverId, "stopping")
        setTimeout(() => updateServerStatus(serverId, "offline"), 2000)
        break
      case "restart":
        updateServerStatus(serverId, "stopping")
        setTimeout(() => updateServerStatus(serverId, "starting"), 1000)
        setTimeout(() => updateServerStatus(serverId, "online"), 3000)
        break
    }
  }

  const handleDeleteClick = (server: any) => {
    setDeleteDialog({ open: true, server })
  }

  const handleDeleteConfirm = () => {
    if (deleteDialog.server) {
      deleteServer(deleteDialog.server.id)
    }
    setDeleteDialog({ open: false, server: null })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">My Servers</h1>
          <p className="text-muted-foreground mt-1">Manage and monitor your game servers</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search servers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 glass-input w-full sm:w-64"
            />
          </div>
          <Link href="/create-server">
            <Button className="premium-button w-full sm:w-auto">
              <Plus className="w-4 h-4 mr-2" />
              Create Server
            </Button>
          </Link>
        </div>
      </div>

      {/* Server Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredServers.map((server) => (
          <Card
            key={server.id}
            className="glass-ultra border border-border hover:shadow-glass-lg transition-all duration-300 rounded-2xl group overflow-hidden"
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between gap-3">
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center shadow-lg flex-shrink-0">
                    <Server className="w-6 h-6 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <CardTitle className="text-foreground text-lg font-bold line-clamp-2 break-words">
                      {server.name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground truncate">{server.game}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Badge className={getStatusColor(server.status)}>
                    {server.status.charAt(0).toUpperCase() + server.status.slice(1)}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 z-10"
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="glass-ultra border-border">
                      <DropdownMenuItem asChild>
                        <Link href={`/servers/${server.id}`} className="flex items-center">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Open Panel
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Settings className="w-4 h-4 mr-2" />
                        Settings
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {server.status === "online" ? (
                        <>
                          <DropdownMenuItem onClick={() => handleServerAction(server.id, "restart")}>
                            <RotateCcw className="w-4 h-4 mr-2" />
                            Restart
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleServerAction(server.id, "stop")}>
                            <Square className="w-4 h-4 mr-2" />
                            Stop
                          </DropdownMenuItem>
                        </>
                      ) : (
                        <DropdownMenuItem onClick={() => handleServerAction(server.id, "start")}>
                          <Play className="w-4 h-4 mr-2" />
                          Start
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(server)}
                        className="text-red-400 focus:text-red-400"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Server Stats */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Users className="w-4 h-4 text-blue-400" />
                  </div>
                  <p className="font-semibold text-foreground">
                    {server.players.current}/{server.players.max}
                  </p>
                  <p className="text-muted-foreground text-xs">Players</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Activity className="w-4 h-4 text-emerald-400" />
                  </div>
                  <p className="font-semibold text-foreground">{server.resources.cpu}%</p>
                  <p className="text-muted-foreground text-xs">CPU</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <HardDrive className="w-4 h-4 text-purple-400" />
                  </div>
                  <p className="font-semibold text-foreground">{server.resources.memory}%</p>
                  <p className="text-muted-foreground text-xs">RAM</p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Link href={`/servers/${server.id}`} className="flex-1">
                  <Button size="sm" className="w-full premium-button">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Manage
                  </Button>
                </Link>
                {server.status === "online" ? (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleServerAction(server.id, "stop")}
                    className="secondary-button bg-transparent text-red-400 hover:text-red-300 hover:bg-red-400/10 flex-shrink-0"
                  >
                    <Square className="w-4 h-4" />
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleServerAction(server.id, "start")}
                    className="secondary-button bg-transparent text-emerald-400 hover:text-emerald-300 hover:bg-emerald-400/10 flex-shrink-0"
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredServers.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-gray-500/20 to-gray-600/20 flex items-center justify-center">
            <Server className="w-8 h-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">No servers found</h3>
          <p className="text-muted-foreground mb-6">
            {searchQuery ? "Try adjusting your search criteria." : "Create your first server to get started!"}
          </p>
          {!searchQuery && (
            <Link href="/create-server">
              <Button className="premium-button">
                <Plus className="w-4 h-4 mr-2" />
                Create Server
              </Button>
            </Link>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, server: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Server"
        description="Are you sure you want to delete this server? This action cannot be undone and all server data will be permanently lost."
        itemName={deleteDialog.server?.name}
        destructive={true}
      />
    </div>
  )
}
