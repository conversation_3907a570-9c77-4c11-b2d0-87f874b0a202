"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  LayoutGrid,
  QrCode,
  Activity,
  Megaphone,
  Database,
  Link2,
  Mail,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  Users,
  Settings,
  Shield,
  FileText,
  BarChart3,
} from "lucide-react"
import { useState } from "react"
import { useAppSettings } from "@/stores/app-settings-store"

const items = [
  { icon: LayoutGrid, label: "Overview", href: "/admin" },
  { icon: Users, label: "Users", href: "/admin/users" },
  { icon: QrCode, label: "Codes", href: "/admin/codes" },
  { icon: BarChart3, label: "Analytics", href: "/admin/analytics" },
  { icon: Megaphone, label: "Announcements", href: "/admin/announcements" },
  { icon: Database, label: "Databases", href: "/admin/databases" },
  { icon: FileText, label: "Logs", href: "/admin/logs" },
  { icon: Activity, label: "Monitoring", href: "/admin/monitoring" },
  { icon: Shield, label: "Security", href: "/admin/security" },
  { icon: Settings, label: "Settings", href: "/admin/settings" },
  { icon: Database, label: "Backups", href: "/admin/backups" },
  { icon: Link2, label: "Links", href: "/admin/links" },
  { icon: Mail, label: "Mail Templates", href: "/admin/mail-templates" },
]

export default function AdminSidebar() {
  const pathname = usePathname()
  const [collapsed, setCollapsed] = useState(false)
  const { logoDataUrl } = useAppSettings()

  return (
    <aside className={`${collapsed ? "w-16" : "w-64"} transition-[width] duration-300 relative z-20 hidden lg:block`}>
      <div className="h-svh glass-ultra border-r border-border shadow-glass">
        <div className="p-4 border-b border-border/50 flex items-center justify-between">
          {!collapsed && (
            <div className="flex items-center gap-3">
              {logoDataUrl ? (
                <img
                  src={logoDataUrl || "/placeholder.svg"}
                  alt="Brand logo"
                  className="w-10 h-10 rounded-xl object-cover border border-border shadow-glass"
                />
              ) : (
                <div className="w-10 h-10 rounded-xl bg-brand border border-border shadow-glass flex items-center justify-center">
                  <LayoutGrid className="w-5 h-5 text-white" />
                </div>
              )}
              <div>
                <h1 className="brand-heading text-xl font-black">Admin</h1>
                <p className="text-muted-foreground text-xs -mt-0.5">Control Center</p>
              </div>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="glass-button p-2 hover:bg-accent/20"
            onClick={() => setCollapsed(!collapsed)}
            aria-label="Toggle admin sidebar"
          >
            {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>

        {/* Go Back Button */}
        <div className="p-4 border-b border-border/50">
          <Link href="/" className="w-full">
            <Button variant="outline" size="sm" className="secondary-button w-full bg-transparent hover:bg-accent/10">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {!collapsed && "Back to Dashboard"}
            </Button>
          </Link>
        </div>

        <nav className="p-4 space-y-2 overflow-y-auto">
          {items.map((item) => {
            const active = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href + "/"))
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 group ${
                  active
                    ? "bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 text-white border border-border/50 shadow-lg"
                    : "text-muted-foreground hover:bg-accent/10 hover:text-foreground hover:shadow-md"
                }`}
              >
                <item.icon
                  className={`w-5 h-5 flex-shrink-0 ${active ? "text-white" : "group-hover:text-foreground"}`}
                />
                {!collapsed && (
                  <span className={`${active ? "text-white" : "group-hover:text-foreground"} font-medium truncate`}>
                    {item.label}
                  </span>
                )}
                {active && !collapsed && <div className="ml-auto w-2 h-2 bg-white rounded-full shadow-sm" />}
              </Link>
            )
          })}
        </nav>
      </div>
    </aside>
  )
}
