"use client"

import PanelLayout from "@/components/panel-layout"
import SettingsGeneral from "@/components/settings-general"
import SettingsAppearance from "@/components/settings-appearance"
import SettingsSecurity from "@/components/settings-security"

export default function SettingsPage() {
  return (
    <PanelLayout title="Settings" subtitle="Account, security, and appearance">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <SettingsGeneral />
          <SettingsSecurity />
        </div>
        <div className="space-y-6">
          <SettingsAppearance />
        </div>
      </div>
    </PanelLayout>
  )
}
