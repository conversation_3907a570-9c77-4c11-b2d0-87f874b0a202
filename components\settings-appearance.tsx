"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useTheme } from "@/components/theme-provider"
import { Moon, Sun } from "lucide-react"
import { useEffect, useState } from "react"

const gradients = [
  { name: "Ocean Blue", from: "#1488cc", to: "#2b32b2", preview: "from-[#1488cc] to-[#2b32b2]" },
  { name: "Crimson Night", from: "#780206", to: "#061161", preview: "from-[#780206] to-[#061161]" },
  { name: "Fuchsia Dream", from: "#D946EF", to: "#4F46E5", preview: "from-fuchsia-500 to-indigo-600" },
  { name: "Emerald Sea", from: "#10B981", to: "#06B6D4", preview: "from-emerald-500 to-cyan-500" },
  { name: "Sunset", from: "#F59E0B", to: "#EF4444", preview: "from-orange-500 to-red-500" },
] as const

export default function SettingsAppearance() {
  const { theme, setTheme } = useTheme()
  const [selected, setSelected] = useState<{ from: string; to: string } | null>(null)

  function setAccent(from: string, to: string) {
    const root = document.documentElement
    root.style.setProperty("--brand-from", from)
    root.style.setProperty("--brand-to", to)
    localStorage.setItem("brand-gradient", JSON.stringify({ from, to }))
  }

  useEffect(() => {
    const saved = localStorage.getItem("brand-gradient")
    if (saved) {
      try {
        const { from, to } = JSON.parse(saved)
        setAccent(from, to)
        setSelected({ from, to })
      } catch {}
    } else {
      // default to new blue gradient
      setAccent(gradients[0].from, gradients[0].to)
      setSelected({ from: gradients[0].from, to: gradients[0].to })
    }
  }, [])

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground text-xl">Appearance</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-foreground font-medium">Theme</p>
            <p className="text-muted-foreground text-sm">Switch between light and dark</p>
          </div>
          <Button
            variant="outline"
            className="secondary-button bg-transparent"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            {theme === "dark" ? <Sun className="w-4 h-4 mr-2" /> : <Moon className="w-4 h-4 mr-2" />}
            {theme === "dark" ? "Light" : "Dark"} Mode
          </Button>
        </div>

        <div className="space-y-3">
          <p className="text-foreground font-medium">Accent Gradient</p>
          <div className="grid grid-cols-2 sm:grid-cols-5 gap-3">
            {gradients.map((g) => (
              <button
                key={g.name}
                onClick={() => {
                  setAccent(g.from, g.to)
                  setSelected({ from: g.from, to: g.to })
                }}
                className={`h-16 rounded-xl w-full bg-gradient-to-br ${g.preview} border border-border shadow-glass hover:opacity-90 transition ring-0 ${selected && selected.from === g.from && selected.to === g.to ? "ring-2 ring-foreground/30" : ""}`}
                title={g.name}
              />
            ))}
          </div>
          <div className="p-3 rounded-xl border border-border/60 glass-ultra">
            <p className="text-xs text-muted-foreground">
              This accent gradient is used across the UI (logo badge, active dots, avatars). Elements using the
              "bg-brand" class will reflect your selection.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
