"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpR<PERSON>, Code, Menu, X } from 'lucide-react'
import Link from "next/link"
import { useState } from "react"

export default function SiteHeader() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <header className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-7xl px-4">
      <nav className="glass-ultra rounded-3xl px-8 py-4 border border-white/20 shadow-glass">
        <div className="flex items-center justify-between w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <div className="relative">
              <div className="w-10 h-10 md:w-12 md:h-12 rounded-xl md:rounded-2xl bg-gradient-to-br from-[#780206] to-[#061161] flex items-center justify-center shadow-lg">
                <Code className="w-5 h-5 md:w-6 md:h-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 md:w-4 md:h-4 bg-emerald-400 rounded-full animate-pulse"></div>
            </div>
            <div>
              <span className="text-xl md:text-2xl font-black bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent">
                Cythro
              </span>
              <p className="text-gray-400 text-xs font-medium -mt-1 hidden sm:block">Digital Creators</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {["Projects", "Services", "About", "Contact"].map((item) => (
              <Link
                key={item}
                href={`#${item.toLowerCase()}`}
                className="text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group"
              >
                {item}
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300"></div>
              </Link>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <Button
              variant="ghost"
              size="sm"
              className="hidden md:inline-flex text-gray-300 hover:text-white hover:bg-white/10 glass-button"
            >
              Say Hello
            </Button>
            <Button size="sm" className="premium-button hidden sm:inline-flex">
              Let's Work Together
              <ArrowUpRight className="ml-2 w-4 h-4" />
            </Button>
            <Button size="sm" className="premium-button sm:hidden">
              Work Together
              <ArrowUpRight className="ml-1 w-4 h-4" />
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-gray-300 hover:text-white p-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu - Improved */}
        {mobileMenuOpen && (
          <div className="lg:hidden mt-6 pt-6 border-t border-white/20">
            <div className="mobile-dropdown rounded-2xl p-6 -mx-4">
              <div className="flex flex-col space-y-6">
                {["Projects", "Services", "About", "Contact"].map((item) => (
                  <Link
                    key={item}
                    href={`#${item.toLowerCase()}`}
                    className="text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 last:border-b-0"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item}
                  </Link>
                ))}
                <div className="pt-4 border-t border-white/10">
                  <Button
                    size="lg"
                    className="w-full premium-button mb-3"
                  >
                    Let's Work Together
                    <ArrowUpRight className="ml-2 w-5 h-5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    className="w-full text-white hover:text-gray-300 hover:bg-white/10 glass-button"
                  >
                    Say Hello
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
