"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { nanoid } from "nanoid"

export type ServerStatus = "online" | "offline" | "starting"
export type ServerType = "Minecraft" | "Rust" | "CS:GO" | "Valheim" | "ARK"

export type Server = {
  id: string
  name: string
  status: ServerStatus
  players: string
  cpu: string
  memory: string
  uptime: string
  type: ServerType
}

type ServerStore = {
  servers: Server[]
  create: (data: Partial<Server>) => Server
  remove: (id: string) => void
  update: (id: string, patch: Partial<Server>) => void
  getById: (id?: string | string[]) => Server | undefined
}

const seed: Server[] = [
  { id: "1", name: "Minecraft Survival", status: "online", players: "12/20", cpu: "45%", memory: "2.1GB/4GB", uptime: "2d 14h", type: "Minecraft" },
  { id: "2", name: "Creative Build Server", status: "online", players: "8/50", cpu: "23%", memory: "1.8GB/8GB", uptime: "5d 2h", type: "Minecraft" },
  { id: "3", name: "Rust PvP Server", status: "offline", players: "0/100", cpu: "0%", memory: "0GB/16GB", uptime: "Offline", type: "Rust" },
  { id: "4", name: "CS:GO Competitive", status: "starting", players: "0/10", cpu: "12%", memory: "0.5GB/2GB", uptime: "Starting...", type: "CS:GO" },
  { id: "5", name: "Valheim Adventure", status: "online", players: "5/10", cpu: "28%", memory: "3.2GB/6GB", uptime: "1d 8h", type: "Valheim" },
  { id: "6", name: "ARK Survival", status: "offline", players: "0/50", cpu: "0%", memory: "0GB/12GB", uptime: "Offline", type: "ARK" },
]

export const useServerStore = create<ServerStore>()(
  persist(
    (set, get) => ({
      servers: seed,
      create: (data) => {
        const newServer: Server = {
          id: nanoid(8),
          name: data.name ?? "New Server",
          status: data.status ?? "offline",
          players: data.players ?? "0/10",
          cpu: data.cpu ?? "0%",
          memory: data.memory ?? "0GB/2GB",
          uptime: data.uptime ?? "Offline",
          type: (data.type as ServerType) ?? "Minecraft",
        }
        set((s) => ({ servers: [newServer, ...s.servers] }))
        return newServer
      },
      remove: (id) => set((s) => ({ servers: s.servers.filter((sv) => sv.id !== id) })),
      update: (id, patch) =>
        set((s) => ({
          servers: s.servers.map((sv) => (sv.id === id ? { ...sv, ...patch } : sv)),
        })),
      getById: (id) => {
        const idStr = Array.isArray(id) ? id[0] : id
        return get().servers.find((sv) => sv.id === idStr)
      },
    }),
    { name: "panel-servers" }
  )
)
