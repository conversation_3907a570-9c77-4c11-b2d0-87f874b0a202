"use client"

import { createContext, useContext, useEffect, useState } from "react"

type Lang = "en" | "es"

type Dict = Record<string, Record<Lang, string>>

const dict: Dict = {
  dashboard: { en: "Dashboard", es: "Panel" },
  managePanel: { en: "Manage your game panel", es: "Administra tu panel de juegos" },
  wallet: { en: "Wallet", es: "Billetera" },
  earn: { en: "Earn", es: "Ganar" },
  redeem: { en: "Redeem", es: "Canjear" },
  referrals: { en: "Referrals", es: "Referidos" },
  transfer: { en: "Transfer", es: "Transferir" },
  servers: { en: "Servers", es: "Servidores" },
  admin: { en: "Admin", es: "Admin" },
  settings: { en: "Settings", es: "Ajustes" },
  createServer: { en: "Create Server", es: "Crear Servidor" },
}

type I18nCtx = {
  lang: Lang
  setLang: (l: Lang) => void
  t: (k: keyof typeof dict) => string
}

const I18nContext = createContext<I18nCtx | undefined>(undefined)

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [lang, setLang] = useState<Lang>(() => {
    if (typeof window === "undefined") return "en"
    return (localStorage.getItem("lang") as Lang) || "en"
  })

  useEffect(() => {
    localStorage.setItem("lang", lang)
  }, [lang])

  const t = (k: keyof typeof dict) => dict[k]?.[lang] ?? k

  return (
    <I18nContext.Provider value={{ lang, setLang, t }}>{children}</I18nContext.Provider>
  )
}

export function useI18n() {
  const ctx = useContext(I18nContext)
  if (!ctx) throw new Error("useI18n must be used within I18nProvider")
  return ctx
}
