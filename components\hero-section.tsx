"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON>, DiscIcon as Discord } from 'lucide-react'

export default function HeroSection() {
  return (
    <section className="pt-32 md:pt-40 pb-16 md:pb-24 relative z-10">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-5xl md:text-7xl lg:text-9xl font-black mb-8 leading-[0.9] tracking-tight">
            <span className="block text-white mb-4">We Turn Ideas Into</span>
            <span className="block bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent enhanced-gradient">
              Digital Magic
            </span>
          </h1>

          <p className="text-xl md:text-2xl lg:text-3xl text-gray-300 mb-16 max-w-4xl mx-auto leading-relaxed font-light">
            Hey there! We're <PERSON><PERSON><PERSON>, and we're obsessed with creating 
            <span className="text-white font-medium"> amazing digital experiences</span>. 
            From games that make you smile to websites that wow your users.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Button size="lg" className="hero-button group w-full sm:w-auto">
              See What We've Built
              <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
            <Button size="lg" variant="outline" className="secondary-button group w-full sm:w-auto">
              <Discord className="mr-3 w-6 h-6" />
              Join Discord
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
