"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Cpu, HardDrive, Activity, Zap } from "lucide-react"

export default function SystemStats() {
  const stats = [
    {
      icon: Cpu,
      label: "CPU",
      value: "34%",
      color: "from-blue-500 to-cyan-500",
      progress: 34,
    },
    {
      icon: HardDrive,
      label: "Memory",
      value: "12.4GB",
      color: "from-purple-500 to-pink-500",
      progress: 62,
    },
    {
      icon: Activity,
      label: "Network",
      value: "1.2GB/s",
      color: "from-emerald-500 to-green-500",
      progress: 45,
    },
    {
      icon: Zap,
      label: "Load",
      value: "2.14",
      color: "from-orange-500 to-red-500",
      progress: 71,
    },
  ]

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader className="pb-3">
        <CardTitle className="text-foreground text-lg">System Stats</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {stats.map((stat, index) => (
          <div key={index} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <stat.icon className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground text-sm font-medium">{stat.label}</span>
              </div>
              <span className="text-foreground font-semibold text-sm">{stat.value}</span>
            </div>
            <div className="w-full bg-muted/20 rounded-full h-2">
              <div
                className={`h-2 rounded-full bg-gradient-to-r ${stat.color} transition-all duration-500`}
                style={{ width: `${stat.progress}%` }}
              />
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
