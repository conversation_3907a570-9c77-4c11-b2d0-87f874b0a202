"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON><PERSON>, CheckCircle, Star } from "lucide-react"

export default function CtaSection() {
  return (
    <section className="py-16 md:py-32 relative z-10">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="glass-ultra rounded-2xl md:rounded-3xl p-8 md:p-16 text-center relative overflow-hidden border border-white/20 shadow-glass">
            <div className="absolute inset-0 bg-gradient-to-r from-[#780206]/10 via-[#780206]/5 to-[#061161]/10"></div>
            <div className="relative z-10">
              <div className="flex justify-center mb-6 md:mb-8">
                {[1, 2, 3, 4, 5].map((i) => (
                  <Star key={i} className="w-6 h-6 md:w-8 md:h-8 text-yellow-400 fill-yellow-400 mx-0.5 md:mx-1" />
                ))}
              </div>
              <h2 className="text-3xl md:text-5xl lg:text-7xl font-black text-white mb-6 md:mb-8">
                Ready to Build
                <span className="block bg-gradient-to-r from-[#780206] via-[#780206] via-pink-400 via-purple-400 to-[#061161] bg-clip-text text-transparent enhanced-gradient">
                  Something Amazing?
                </span>
              </h2>
              <p className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-12 md:mb-16 max-w-3xl mx-auto font-light">
                Let's bring your vision to life. From concept to deployment, we're here to help.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 md:gap-8 justify-center items-center mb-8 md:mb-12">
                <div className="relative w-full sm:w-auto">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="w-full sm:w-80 md:w-96 glass-ultra border-white/30 text-white placeholder:text-gray-400 focus:border-white/50 transition-all duration-300 rounded-xl md:rounded-2xl px-6 md:px-8 py-4 md:py-6 text-base md:text-lg font-medium shadow-glass"
                  />
                </div>
                <Button size="lg" className="cta-button group w-full sm:w-auto">
                  Start Your Project
                  <ArrowRight className="ml-2 md:ml-3 w-5 h-5 md:w-6 md:h-6 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </div>

              <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-12 text-gray-400">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-emerald-400" />
                  <span className="font-medium text-sm md:text-base">Free consultation</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-emerald-400" />
                  <span className="font-medium text-sm md:text-base">Custom solutions</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-emerald-400" />
                  <span className="font-medium text-sm md:text-base">Ongoing support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
