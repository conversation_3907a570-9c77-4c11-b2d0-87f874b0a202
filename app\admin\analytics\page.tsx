"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function AdminAnalyticsPage() {
  const stats = [
    { label: "Daily signups", value: 42, color: "from-emerald-500 to-green-500" },
    { label: "Servers created", value: 17, color: "from-blue-500 to-cyan-500" },
    { label: "Coins used", value: 1230, color: "from-orange-500 to-red-500" },
  ]

  return (
    <AdminLayout title="Analytics" subtitle="Platform usage overview">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {stats.map((s, i) => (
          <Card key={i} className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardHeader>
              <CardTitle className="text-foreground">{s.label}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-3xl font-black text-foreground">{s.value}</span>
                <div className={`w-14 h-14 rounded-xl bg-gradient-to-br ${s.color}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </AdminLayout>
  )
}
