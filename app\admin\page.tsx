"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  Server,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Database,
} from "lucide-react"

export default function AdminPage() {
  const systemStats = [
    {
      title: "Total Users",
      value: "2,847",
      change: "+12%",
      trend: "up",
      icon: Users,
      color: "from-blue-500 to-cyan-500",
    },
    {
      title: "Active Servers",
      value: "156",
      change: "+8%",
      trend: "up",
      icon: Server,
      color: "from-emerald-500 to-green-500",
    },
    {
      title: "System Load",
      value: "34%",
      change: "-5%",
      trend: "down",
      icon: Activity,
      color: "from-orange-500 to-red-500",
    },
    {
      title: "Revenue",
      value: "$12,847",
      change: "+23%",
      trend: "up",
      icon: TrendingUp,
      color: "from-purple-500 to-pink-500",
    },
  ]

  const systemHealth = [
    { name: "CPU Usage", value: 34, status: "good", color: "bg-emerald-500" },
    { name: "Memory Usage", value: 67, status: "warning", color: "bg-yellow-500" },
    { name: "Disk Usage", value: 23, status: "good", color: "bg-emerald-500" },
    { name: "Network Load", value: 89, status: "critical", color: "bg-red-500" },
  ]

  const recentActivity = [
    {
      id: 1,
      action: "New user registered",
      user: "<EMAIL>",
      time: "2 minutes ago",
      type: "user",
      icon: Users,
    },
    {
      id: 2,
      action: "Server created",
      user: "admin",
      time: "5 minutes ago",
      type: "server",
      icon: Server,
    },
    {
      id: 3,
      action: "Payment processed",
      user: "<EMAIL>",
      time: "8 minutes ago",
      type: "payment",
      icon: TrendingUp,
    },
    {
      id: 4,
      action: "System backup completed",
      user: "system",
      time: "15 minutes ago",
      type: "system",
      icon: CheckCircle,
    },
    {
      id: 5,
      action: "Security alert resolved",
      user: "security-bot",
      time: "23 minutes ago",
      type: "security",
      icon: AlertTriangle,
    },
  ]

  const serverStatus = [
    { name: "Web Server", status: "online", uptime: "99.9%", load: 23 },
    { name: "Database", status: "online", uptime: "99.8%", load: 45 },
    { name: "Game Nodes", status: "online", uptime: "99.7%", load: 67 },
    { name: "CDN", status: "maintenance", uptime: "98.2%", load: 12 },
    { name: "Backup System", status: "online", uptime: "100%", load: 8 },
  ]

  return (
    <AdminLayout title="Admin Overview" subtitle="System status and management dashboard">
      <div className="space-y-6">
        {/* System Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {systemStats.map((stat, index) => (
            <Card key={index} className="glass-ultra border border-border shadow-glass rounded-2xl overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-muted-foreground text-sm font-medium">{stat.title}</p>
                    <p className="text-3xl font-black text-foreground mt-1">{stat.value}</p>
                  </div>
                  <div
                    className={`w-12 h-12 rounded-xl bg-gradient-to-br ${stat.color} flex items-center justify-center shadow-lg`}
                  >
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className={`flex items-center gap-1 ${stat.trend === "up" ? "text-emerald-400" : "text-red-400"}`}
                  >
                    {stat.trend === "up" ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    <span className="text-xs font-medium">{stat.change}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">vs last month</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* System Health */}
          <Card className="glass-ultra border border-border shadow-glass rounded-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemHealth.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{item.name}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">{item.value}%</span>
                      <Badge
                        variant={
                          item.status === "good" ? "default" : item.status === "warning" ? "secondary" : "destructive"
                        }
                        className="text-xs"
                      >
                        {item.status}
                      </Badge>
                    </div>
                  </div>
                  <Progress value={item.value} className="h-2" />
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Server Status */}
          <Card className="glass-ultra border border-border shadow-glass rounded-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="w-5 h-5" />
                Server Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {serverStatus.map((server, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg bg-accent/5 border border-border/50"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        server.status === "online"
                          ? "bg-emerald-500"
                          : server.status === "maintenance"
                            ? "bg-yellow-500"
                            : "bg-red-500"
                      } animate-pulse`}
                    />
                    <div>
                      <p className="text-sm font-medium">{server.name}</p>
                      <p className="text-xs text-muted-foreground">Uptime: {server.uptime}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{server.load}%</p>
                    <p className="text-xs text-muted-foreground">Load</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="glass-ultra border border-border shadow-glass rounded-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentActivity.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start gap-3 p-3 rounded-lg bg-accent/5 border border-border/50"
                >
                  <div
                    className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      activity.type === "user"
                        ? "bg-blue-500/20 text-blue-500"
                        : activity.type === "server"
                          ? "bg-emerald-500/20 text-emerald-500"
                          : activity.type === "payment"
                            ? "bg-purple-500/20 text-purple-500"
                            : activity.type === "system"
                              ? "bg-cyan-500/20 text-cyan-500"
                              : "bg-red-500/20 text-red-500"
                    }`}
                  >
                    <activity.icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.action}</p>
                    <p className="text-xs text-muted-foreground truncate">{activity.user}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="glass-ultra border border-border shadow-glass rounded-2xl">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button className="p-4 rounded-xl bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-200 group">
                <Users className="w-8 h-8 text-blue-500 mb-2 group-hover:scale-110 transition-transform" />
                <p className="text-sm font-medium">Manage Users</p>
              </button>
              <button className="p-4 rounded-xl bg-gradient-to-br from-emerald-500/10 to-green-500/10 border border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-200 group">
                <Server className="w-8 h-8 text-emerald-500 mb-2 group-hover:scale-110 transition-transform" />
                <p className="text-sm font-medium">Server Control</p>
              </button>
              <button className="p-4 rounded-xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-200 group">
                <Database className="w-8 h-8 text-purple-500 mb-2 group-hover:scale-110 transition-transform" />
                <p className="text-sm font-medium">Database</p>
              </button>
              <button className="p-4 rounded-xl bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 hover:border-orange-500/40 transition-all duration-200 group">
                <AlertTriangle className="w-8 h-8 text-orange-500 mb-2 group-hover:scale-110 transition-transform" />
                <p className="text-sm font-medium">Security</p>
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
