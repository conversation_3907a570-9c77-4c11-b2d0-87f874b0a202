"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { nanoid } from "nanoid"

type Referral = { id: string; email: string; coins: number; date: string }

type ReferralStore = {
  link?: string
  referrals: Referral[]
  ensureLink: () => string
  addReferral: (email: string, coins: number) => void
}

export const useReferralStore = create<ReferralStore>()(
  persist(
    (set, get) => ({
      link: undefined,
      referrals: [],
      ensureLink: () => {
        const state = get()
        if (state.link) return state.link
        const url = `${location.origin}/ref/${nanoid(8)}`
        set({ link: url })
        return url
      },
      addReferral: (email, coins) => set(s => ({ referrals: [{ id: nanoid(), email, coins, date: new Date().toISOString() }, ...s.referrals] })),
    }),
    { name: "referrals" }
  )
)
