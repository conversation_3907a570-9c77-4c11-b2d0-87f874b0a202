"use client"

import { useLinkStore } from "@/stores/link-store"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { Trash2, Copy } from 'lucide-react'
import { useToast } from "@/hooks/use-toast"

export default function AdminLinks() {
  const { links, add, remove } = useLinkStore()
  const [slug, setSlug] = useState("")
  const [target, setTarget] = useState("")
  const { toast } = useToast()

  const addLink = () => {
    if (!slug.trim() || !target.trim()) return
    add({ slug, target })
    setSlug("")
    setTarget("")
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader><CardTitle className="text-foreground">Create Link</CardTitle></CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <Input placeholder="slug (e.g. summer)" value={slug} onChange={(e) => setSlug(e.target.value)} className="glass-ultra border-border" />
          <Input placeholder="https://target.url" value={target} onChange={(e) => setTarget(e.target.value)} className="glass-ultra border-border md:col-span-2" />
        </div>
        <div className="flex justify-end"><Button className="premium-button" onClick={addLink}>Add</Button></div>

        <div className="space-y-2">
          {links.length === 0 ? <p className="text-sm text-muted-foreground">{'No links yet.'}</p> : links.map(l => {
            const url = `${location.origin}/r/${l.slug}`
            return (
              <div key={l.slug} className="flex items-center justify-between p-3 glass-ultra rounded-xl border border-border/50">
                <div>
                  <p className="text-foreground">{url}</p>
                  <p className="text-xs text-muted-foreground">→ {l.target}</p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" className="glass-button p-2" onClick={async () => { await navigator.clipboard.writeText(url); toast({ title: "Copied", description: url })}}>
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" className="glass-button p-2 text-red-400 hover:text-red-300" onClick={() => remove(l.slug)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
