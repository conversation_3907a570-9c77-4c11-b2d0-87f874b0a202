"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON>ertTriangle, Lock, Eye, Ban } from "lucide-react"

export default function AdminSecurityPage() {
  const securityEvents = [
    {
      time: "2 min ago",
      type: "login_attempt",
      severity: "high",
      message: "Failed login attempt from *************",
      user: "<EMAIL>",
    },
    {
      time: "15 min ago",
      type: "permission_change",
      severity: "medium",
      message: "User role changed to Admin",
      user: "<EMAIL>",
    },
    { time: "1h ago", type: "api_access", severity: "low", message: "API key generated", user: "<EMAIL>" },
    {
      time: "2h ago",
      type: "suspicious",
      severity: "high",
      message: "Multiple failed 2FA attempts",
      user: "<EMAIL>",
    },
  ]

  const blockedIPs = [
    { ip: "*************", reason: "Brute force attack", blocked: "2h ago", attempts: 15 },
    { ip: "*********", reason: "Suspicious activity", blocked: "1d ago", attempts: 8 },
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "text-red-400 bg-red-400/10 border-red-400/20"
      case "medium":
        return "text-yellow-400 bg-yellow-400/10 border-yellow-400/20"
      case "low":
        return "text-blue-400 bg-blue-400/10 border-blue-400/20"
      default:
        return "text-muted-foreground bg-muted/10 border-border"
    }
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case "login_attempt":
        return Lock
      case "permission_change":
        return Shield
      case "api_access":
        return Eye
      case "suspicious":
        return AlertTriangle
      default:
        return Shield
    }
  }

  return (
    <AdminLayout title="Security Center" subtitle="Monitor threats and manage access controls">
      <div className="space-y-6">
        {/* Security Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-muted-foreground text-sm">Threat Level</p>
                  <p className="text-2xl font-black text-yellow-400">Medium</p>
                </div>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-500 flex items-center justify-center shadow-lg">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-muted-foreground text-sm">Blocked IPs</p>
                  <p className="text-2xl font-black text-foreground">{blockedIPs.length}</p>
                </div>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center shadow-lg">
                  <Ban className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-muted-foreground text-sm">Active Sessions</p>
                  <p className="text-2xl font-black text-foreground">12</p>
                </div>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-emerald-500 to-green-500 flex items-center justify-center shadow-lg">
                  <Eye className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Security Events */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-foreground">Recent Security Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {securityEvents.map((event, index) => {
                const Icon = getEventIcon(event.type)
                return (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 glass-ultra rounded-xl border border-border/50"
                  >
                    <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center flex-shrink-0">
                      <Icon className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge className={`text-xs font-medium ${getSeverityColor(event.severity)}`}>
                          {event.severity.toUpperCase()}
                        </Badge>
                        <span className="text-muted-foreground text-xs">{event.time}</span>
                      </div>
                      <p className="text-foreground font-medium text-sm">{event.message}</p>
                      <p className="text-muted-foreground text-xs">User: {event.user}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Blocked IPs */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-foreground">Blocked IP Addresses</CardTitle>
              <Button variant="outline" className="secondary-button bg-transparent">
                <Ban className="w-4 h-4 mr-2" />
                Block IP
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {blockedIPs.map((blocked, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50"
                >
                  <div>
                    <p className="text-foreground font-medium font-mono">{blocked.ip}</p>
                    <p className="text-muted-foreground text-sm">{blocked.reason}</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-foreground text-sm font-medium">{blocked.attempts} attempts</p>
                      <p className="text-muted-foreground text-xs">Blocked {blocked.blocked}</p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="secondary-button bg-transparent text-red-400 hover:text-red-300"
                    >
                      Unblock
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
