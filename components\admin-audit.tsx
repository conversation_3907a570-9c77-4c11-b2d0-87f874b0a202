"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Shield, Server, Settings, User } from 'lucide-react'

export default function AdminAudit() {
  const items = [
    { icon: User, title: "User invited", desc: "Invited <EMAIL>", time: "5m ago", color: "from-emerald-500 to-green-500" },
    { icon: Server, title: "Server created", desc: "Valheim Adventure", time: "16m ago", color: "from-blue-500 to-cyan-500" },
    { icon: Settings, title: "Settings updated", desc: "Theme set to Light", time: "1h ago", color: "from-purple-500 to-pink-500" },
    { icon: Shield, title: "Suspicious login", desc: "Blocked attempt from DE", time: "2h ago", color: "from-orange-500 to-red-500" },
  ]
  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="text-foreground text-xl">Admin Activity</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.map((a, i) => (
          <div key={i} className="flex items-start gap-3">
            <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${a.color} flex items-center justify-center`}>
              <a.icon className="w-4 h-4 text-white" />
            </div>
            <div className="min-w-0">
              <p className="text-foreground font-medium text-sm">{a.title}</p>
              <p className="text-muted-foreground text-xs">{a.desc}</p>
              <p className="text-muted-foreground text-xs mt-1">{a.time}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
