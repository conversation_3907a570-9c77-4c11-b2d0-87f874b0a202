"use client"

import AdminLayout from "@/components/admin-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Activity, Cpu, HardDrive, Network, Server } from "lucide-react"

export default function AdminMonitoringPage() {
  const metrics = [
    { name: "CPU Usage", value: "34%", icon: Cpu, color: "from-blue-500 to-cyan-500", trend: "+2%" },
    { name: "Memory", value: "12.4GB", icon: HardDrive, color: "from-purple-500 to-pink-500", trend: "+0.8GB" },
    { name: "Network I/O", value: "1.2GB/s", icon: Network, color: "from-emerald-500 to-green-500", trend: "+0.3GB/s" },
    { name: "Active Servers", value: "6", icon: Server, color: "from-orange-500 to-red-500", trend: "+1" },
  ]

  const servers = [
    { name: "Minecraft Survival", status: "healthy", cpu: "45%", memory: "2.1GB", uptime: "2d 14h" },
    { name: "Rust PvP", status: "warning", cpu: "78%", memory: "4.8GB", uptime: "1d 8h" },
    { name: "Creative Build", status: "healthy", cpu: "23%", memory: "1.8GB", uptime: "5d 2h" },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "text-emerald-400 bg-emerald-400/10"
      case "warning":
        return "text-yellow-400 bg-yellow-400/10"
      case "critical":
        return "text-red-400 bg-red-400/10"
      default:
        return "text-muted-foreground bg-muted/10"
    }
  }

  return (
    <AdminLayout title="System Monitoring" subtitle="Real-time performance metrics and alerts">
      <div className="space-y-6">
        {/* Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <Card
              key={index}
              className="glass-ultra border border-border rounded-2xl shadow-glass hover:shadow-glass-lg transition-all duration-300"
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-muted-foreground text-sm font-medium">{metric.name}</p>
                    <p className="text-2xl font-black text-foreground">{metric.value}</p>
                  </div>
                  <div
                    className={`w-12 h-12 rounded-xl bg-gradient-to-br ${metric.color} flex items-center justify-center shadow-lg`}
                  >
                    <metric.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Activity className="w-3 h-3 text-emerald-400" />
                  <span className="text-xs text-emerald-400 font-medium">{metric.trend}</span>
                  <span className="text-xs text-muted-foreground">vs last hour</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Server Health */}
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader>
            <CardTitle className="text-foreground">Server Health Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {servers.map((server, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 rounded-xl bg-brand flex items-center justify-center shadow-lg">
                      <Server className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-foreground font-medium">{server.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium capitalize ${getStatusColor(server.status)}`}
                        >
                          {server.status}
                        </span>
                        <span className="text-muted-foreground text-xs">Uptime: {server.uptime}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-6 text-sm">
                    <div className="text-center">
                      <p className="text-muted-foreground">CPU</p>
                      <p className="text-foreground font-medium">{server.cpu}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-muted-foreground">Memory</p>
                      <p className="text-foreground font-medium">{server.memory}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
