"use client"

import PanelLayout from "@/components/panel-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import ServersGridPage from "@/components/servers-grid-page"
import { useRouter } from "next/navigation"

export default function ServersPage() {
  const router = useRouter()

  return (
    <PanelLayout title="Servers" subtitle="Create and manage game servers">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-foreground">Your Servers</h3>
        <Button className="premium-button" onClick={() => router.push("/create-server")}>
          <Plus className="w-4 h-4 mr-2" />
          Create Server
        </Button>
      </div>

      <ServersGridPage />
    </PanelLayout>
  )
}
