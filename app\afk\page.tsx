"use client"

import PanelLayout from "@/components/panel-layout"
import { useEffect, useRef, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useWalletStore } from "@/stores/wallet-store"

export default function AfkPage() {
  const [active, setActive] = useState(true)
  const timer = useRef<NodeJS.Timeout | null>(null)
  const { addCoins } = useWalletStore()

  useEffect(() => {
    const onAny = () => setActive(true)
    const onIdle = () => setActive(false)
    let idleTimeout: NodeJS.Timeout | null = null
    const resetIdle = () => {
      setActive(true)
      if (idleTimeout) clearTimeout(idleTimeout)
      idleTimeout = setTimeout(onIdle, 60000) // 1 min idle
    }
    window.addEventListener("mousemove", resetIdle)
    window.addEventListener("keydown", resetIdle)
    resetIdle()
    timer.current = setInterval(() => {
      if (!active) addCoins(1, "AFK reward")
    }, 60000)
    return () => {
      window.removeEventListener("mousemove", resetIdle)
      window.removeEventListener("keydown", resetIdle)
      if (timer.current) clearInterval(timer.current)
      if (idleTimeout) clearTimeout(idleTimeout)
    }
  }, [active, addCoins])

  return (
    <PanelLayout title="AFK Page" subtitle="Earn passive coins while idle (demo)">
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass max-w-xl">
        <CardHeader><CardTitle className="text-foreground">AFK Rewards</CardTitle></CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">{active ? "Active (move mouse to reset idle timer)" : "AFK mode active: earning 1 coin/min"}</p>
          <Button variant="outline" className="secondary-button" onClick={() => setActive(!active)}>{active ? "Go AFK" : "I'm Back"}</Button>
        </CardContent>
      </Card>
    </PanelLayout>
  )
}
