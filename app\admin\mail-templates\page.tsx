"use client"

import AdminLayout from "@/components/admin-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function MailTemplatesPage() {
  return (
    <AdminLayout title="Mail Templates" subtitle="Preview welcome, verification, and reset emails">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {["Welcome", "Verify Email", "Reset Password"].map((name) => (
          <Card key={name} className="glass-ultra border border-border rounded-2xl shadow-glass">
            <CardHeader><CardTitle className="text-foreground">{name}</CardTitle></CardHeader>
            <CardContent>
              <div className="rounded-xl border border-border/50 p-4 bg-background/60">
                <p className="text-sm text-muted-foreground">{'Email preview content...'}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </AdminLayout>
  )
}
