"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"

interface CreateDatabaseDialogProps {
  children: React.ReactNode
}

export function CreateDatabaseDialog({ children }: CreateDatabaseDialogProps) {
  const [open, setOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    type: "",
    description: "",
    maxConnections: "100",
    enableBackups: true,
    backupSchedule: "daily",
  })
  const { toast } = useToast()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!formData.name || !formData.type) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // Simulate database creation
    toast({
      title: "Database Created",
      description: `Database "${formData.name}" has been created successfully.`,
    })

    // Reset form and close dialog
    setFormData({
      name: "",
      type: "",
      description: "",
      maxConnections: "100",
      enableBackups: true,
      backupSchedule: "daily",
    })
    setOpen(false)
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="glass-ultra border-border max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="text-foreground">Create New Database</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Set up a new database instance with your preferred configuration.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            {/* Database Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-foreground font-medium">
                Database Name *
              </Label>
              <Input
                id="name"
                placeholder="e.g., production_db"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className="glass-input"
                required
              />
            </div>

            {/* Database Type */}
            <div className="space-y-2">
              <Label htmlFor="type" className="text-foreground font-medium">
                Database Type *
              </Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange("type", value)}>
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder="Select database type" />
                </SelectTrigger>
                <SelectContent className="glass-ultra border-border">
                  <SelectItem value="mysql">MySQL 8.0</SelectItem>
                  <SelectItem value="postgresql">PostgreSQL 15</SelectItem>
                  <SelectItem value="redis">Redis 7.0</SelectItem>
                  <SelectItem value="mongodb">MongoDB 6.0</SelectItem>
                  <SelectItem value="mariadb">MariaDB 10.11</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-foreground font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                placeholder="Brief description of the database purpose..."
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="glass-input resize-none"
                rows={3}
              />
            </div>

            {/* Max Connections */}
            <div className="space-y-2">
              <Label htmlFor="maxConnections" className="text-foreground font-medium">
                Max Connections
              </Label>
              <Input
                id="maxConnections"
                type="number"
                min="1"
                max="1000"
                value={formData.maxConnections}
                onChange={(e) => handleInputChange("maxConnections", e.target.value)}
                className="glass-input"
              />
            </div>

            {/* Backup Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-foreground font-medium">Enable Backups</Label>
                  <p className="text-sm text-muted-foreground">Automatically backup your database</p>
                </div>
                <Switch
                  checked={formData.enableBackups}
                  onCheckedChange={(checked) => handleInputChange("enableBackups", checked)}
                />
              </div>

              {formData.enableBackups && (
                <div className="space-y-2">
                  <Label htmlFor="backupSchedule" className="text-foreground font-medium">
                    Backup Schedule
                  </Label>
                  <Select
                    value={formData.backupSchedule}
                    onValueChange={(value) => handleInputChange("backupSchedule", value)}
                  >
                    <SelectTrigger className="glass-input">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-ultra border-border">
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="secondary-button bg-transparent"
            >
              Cancel
            </Button>
            <Button type="submit" className="premium-button">
              Create Database
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
