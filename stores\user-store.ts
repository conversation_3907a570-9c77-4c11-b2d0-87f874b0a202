"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { nanoid } from "nanoid"

export type Role = "Admin" | "Moderator" | "User"

export type PanelUser = {
  id: string
  name: string
  email: string
  role: Role
  active: boolean
}

type UserStore = {
  users: PanelUser[]
  create: (data: Omit<PanelUser, "id">) => PanelUser
  remove: (id: string) => void
  update: (id: string, patch: Partial<PanelUser>) => void
}

const seed: PanelUser[] = [
  { id: "u1", name: "Admin User", email: "<EMAIL>", role: "Admin", active: true },
  { id: "u2", name: "<PERSON>", email: "<EMAIL>", role: "Moderator", active: true },
  { id: "u3", name: "<PERSON>", email: "<EMAIL>", role: "User", active: true },
  { id: "u4", name: "<PERSON>", email: "<EMAIL>", role: "Admin", active: false },
]

export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      users: seed,
      create: (data) => {
        const user: PanelUser = { id: nanoid(8), ...data }
        set((s) => ({ users: [user, ...s.users] }))
        return user
      },
      remove: (id) => set((s) => ({ users: s.users.filter((u) => u.id !== id) })),
      update: (id, patch) =>
        set((s) => ({
          users: s.users.map((u) => (u.id === id ? { ...u, ...patch } : u)),
        })),
    }),
    { name: "panel-users" }
  )
)
