"use client"

import { useState } from "react"
import PterodactylSidebar from "@/components/pterodactyl-sidebar"
import PterodactylHeader from "@/components/pterodactyl-header"
import { ThemeProvider } from "@/components/theme-provider"
import { I18nProvider } from "@/components/i18n-provider"
import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"

type PanelLayoutProps = {
  title: string
  subtitle?: string
  children: React.ReactNode
}

export default function PanelLayout({
  title,
  subtitle = "Manage your game panel",
  children,
}: PanelLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <ThemeProvider>
      <I18nProvider>
        {/* Page shell never scrolls; only the content area scrolls */}
        <div className="relative h-svh w-svw overflow-hidden bg-background">
          {/* Decorative background */}
          <div aria-hidden className="pointer-events-none fixed inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background" />
            <div className="absolute top-1/4 left-1/4 w-96 h-96 md:w-[500px] md:h-[500px] bg-gradient-radial from-[oklch(0.45_0.2_29)]/35 via-[oklch(0.45_0.2_29)]/15 to-transparent rounded-full blur-3xl animate-pulse-glow" />
            <div className="absolute bottom-1/3 right-1/4 w-[500px] h-[500px] md:w-[600px] md:h-[600px] bg-gradient-radial from-[oklch(0.45_0.2_270)]/35 via-[oklch(0.45_0.2_270)]/15 to-transparent rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: "1s" }} />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] md:w-[800px] md:h-[800px] bg-gradient-conic from-[oklch(0.45_0.2_29)]/25 via-fuchsia-500/10 to-[oklch(0.45_0.2_270)]/25 rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: "2s" }} />
            <div className="absolute inset-0 bg-grid-pattern opacity-30" />
          </div>

          {/* Mobile overlay to close sidebar */}
          {sidebarOpen && (
            <button
              aria-label="Close sidebar"
              className="fixed inset-0 z-40 bg-black/50 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* App frame */}
          <div className="relative z-10 flex h-full">
            {/* Left: Sidebar (desktop static, mobile drawer) */}
            <PterodactylSidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

            {/* Right: Header + Scrollable content column */}
            <div className="flex min-w-0 flex-1 flex-col">
              {/* Sticky header stays visible while content scrolls */}
              <div className="sticky top-0 z-30">
                <SidebarInset>
                  <PterodactylHeader title={title} subtitle={subtitle} />
                  <main className="flex-1 p-6 bg-background">
                    <div className="max-w-7xl mx-auto">
                      {children}
                    </div>
                  </main>
                </SidebarInset>
              </div>

              {/* Scrollable content area; header and sidebar stay still */}
              {/* <main
                className="flex-1 overflow-y-auto px-4 py-4 md:px-6 md:py-6"
                style={{
                  WebkitOverflowScrolling: "touch",
                  contain: "content",
                }}
              >
                {/* Safe-area padding for very small devices */}
                {/* <div className="pt-[env(safe-area-inset-top)] pb-[env(safe-area-inset-bottom)]">
                  {children}
                </div>
              </main> */}
            </div>
          </div>
        </div>
      </I18nProvider>
    </ThemeProvider>
  )
}
