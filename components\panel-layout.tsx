"use client"

import { PterodactylSidebarProvider } from "@/components/pterodactyl-sidebar"
import { PterodactylHeader } from "@/components/pterodactyl-header"
import { ThemeProvider } from "@/components/theme-provider"
import { I18nProvider } from "@/components/i18n-provider"
import { SidebarInset } from "@/components/ui/sidebar"

type PanelLayoutProps = {
  title: string
  subtitle?: string
  children: React.ReactNode
}

export default function PanelLayout({
  title,
  subtitle = "Manage your game panel",
  children,
}: PanelLayoutProps) {

  return (
    <ThemeProvider>
      <I18nProvider>
        <PterodactylSidebarProvider>
          {/* Page shell never scrolls; only the content area scrolls */}
          <div className="relative h-svh w-svw overflow-hidden bg-background">
            {/* Decorative background */}
            <div aria-hidden className="pointer-events-none fixed inset-0 z-0">
              <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background" />
              <div className="absolute top-1/4 left-1/4 w-96 h-96 md:w-[500px] md:h-[500px] bg-gradient-radial from-[oklch(0.45_0.2_29)]/35 via-[oklch(0.45_0.2_29)]/15 to-transparent rounded-full blur-3xl animate-pulse-glow" />
              <div className="absolute bottom-1/3 right-1/4 w-[500px] h-[500px] md:w-[600px] md:h-[600px] bg-gradient-radial from-[oklch(0.45_0.2_270)]/35 via-[oklch(0.45_0.2_270)]/15 to-transparent rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: "1s" }} />
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] md:w-[800px] md:h-[800px] bg-gradient-conic from-[oklch(0.45_0.2_29)]/25 via-fuchsia-500/10 to-[oklch(0.45_0.2_270)]/25 rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: "2s" }} />
              <div className="absolute inset-0 bg-grid-pattern opacity-30" />
            </div>

            <SidebarInset>
              <PterodactylHeader title={title} subtitle={subtitle} />
              <main className="flex-1 p-6 bg-background">
                <div className="max-w-7xl mx-auto">
                  {children}
                </div>
              </main>
            </SidebarInset>
          </div>
        </PterodactylSidebarProvider>
      </I18nProvider>
    </ThemeProvider>
  )
}
